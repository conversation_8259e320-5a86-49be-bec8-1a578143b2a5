import React, {useCallback, useEffect, useState} from 'react';
import {useFocusEffect} from '@react-navigation/native';
import {Image, ImageBackground, Text, View} from 'react-native';
import {colors, images} from '../../constants';
import styles from './ConfirmStyle';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import FadingLine from '../../components/FadingLine/FadingLine';
import Button from '../../components/Button/Button';
import {useLocationContext} from '../../utils/LocationContext';
import {SafeAreaView} from 'react-native-safe-area-context';
import {spacing} from '../../constants/theme';
import {useTranslation} from 'react-i18next';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import diamondInactive from '../../icons/diamondGrey.svg';
import ellipseActive from '../../icons/ellipseActive.svg';
import RideService from '../../services/RideService';
import {useRideDetails} from '../../hooks/useRideDetails';
import {STATUS_CODE} from '../../constants/constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useToast} from '../../components/Toast/Toast';
import TripService from '../../services/TripService';
import TripStatusManager from '../../utils/TripStatusManager';
import NavigationGuard from '../../utils/NavigationGuard';
import { useNavigationDebug } from '../../hooks/useNavigationDebug';

interface ConfirmScreenProps {
  navigation: any;
  route: any;
}

const Confirm: React.FC<ConfirmScreenProps> = ({navigation}) => {
  const {t} = useTranslation();
  const {showToast} = useToast();
  const {pickupAddress, whereto} = useLocationContext();
  const {
    setTripDetails,
    tripDetails,
    tripId,
    fetchTripDetails,
    clearLocalStorage,
    pausePolling,
    resumePolling,
  } = useRideDetails();
  const [isLoading, setIsLoading] = useState(true);

  const tripStatusManager = TripStatusManager.getInstance();
  const navigationGuard = NavigationGuard.getInstance();
  const { logAction, logError } = useNavigationDebug('Confirm');

  // Single effect to handle initial setup and navigation
  useEffect(() => {
    const initializeConfirmScreen = async () => {
      try {
        // Check if we should navigate away due to no drivers
        const isNoDrivers = await tripStatusManager.isNoDriversScenario();
        if (isNoDrivers) {
          await tripStatusManager.clearNoDriversFlag();
          await clearLocalStorage();

          if (await navigationGuard.canNavigate('Direction')) {
            await navigationGuard.startNavigation('Direction');
            navigation.reset({
              index: 0,
              routes: [{name: 'Direction'}],
            });
            setTimeout(() => navigationGuard.completeNavigation(), 500);
          }
          return;
        }

        // Initialize trip details
        await fetchTripDetails();
        setIsLoading(false);
      } catch (error) {
        console.error('[Confirm] Initialization error:', error);
        setIsLoading(false);
      }
    };

    initializeConfirmScreen();
  }, []);

  // Simplified polling with navigation guard and status tracking
  useFocusEffect(
    useCallback(() => {
      let pollingInterval: NodeJS.Timeout;
      let shouldStopPolling = false;
      let lastKnownStatus: string | null = null;

      // Pause global polling when Confirm screen is active
      pausePolling();
      console.log('[Confirm] Paused global polling');

      const pollRideStatus = async () => {
        if (shouldStopPolling) return;

        try {
          const response = await TripService.getActiveRide();
          const activeRide = (response as any)?.data?.data?.activeRide;

          if (!activeRide) {
            console.log('[Confirm] No active ride found - handling no drivers scenario');
            logAction('No active ride found, handling as no drivers available');
            shouldStopPolling = true;
            clearInterval(pollingInterval);

            // Force reset all state for no drivers scenario
            await tripStatusManager.handleNoDriversAvailable();
            await clearLocalStorage();
            return;
          }

          // Only process status changes, ignore duplicate statuses
          if (activeRide.status !== lastKnownStatus) {
            console.log('[Confirm] Status changed from', lastKnownStatus, 'to', activeRide.status);
            lastKnownStatus = activeRide.status;

            if (activeRide.status !== 'processing') {
              shouldStopPolling = true;
              clearInterval(pollingInterval);
              setTripDetails(activeRide);

              // Handle specific statuses
              if (activeRide.status === 'no_drivers_available') {
                console.log('[Confirm] No drivers available for current ride');
                logAction('No drivers available, handling via TripStatusManager');
                await tripStatusManager.handleNoDriversAvailable();
                return;
              }

              // Update status without auto-navigation for other statuses
              await tripStatusManager.updateTripStatus(activeRide.status, activeRide.id, false);

              // Manual navigation based on status
              const navigationRoutes: Record<string, string> = {
                accepted: 'RideDetails',
                aborted: 'BottomTab',
                completed: 'CollectCash',
                driver_cancelled: 'Direction',
                verified: 'RideRoute',
              };

              const targetRoute = navigationRoutes[activeRide.status];
              if (targetRoute && await navigationGuard.canNavigate(targetRoute)) {
                await navigationGuard.startNavigation(targetRoute);
                navigation.reset({
                  index: 0,
                  routes: [{name: targetRoute}],
                });
                setTimeout(() => navigationGuard.completeNavigation(), 500);
              }
              return;
            }
          } else {
            console.log('[Confirm] Status unchanged:', activeRide.status);
          }

          setTripDetails(activeRide);
        } catch (error) {
          console.error('[Confirm] Polling error:', error);
        }
      };

      // Set initial status
      const currentStatus = tripStatusManager.getCurrentStatus();
      lastKnownStatus = currentStatus?.status || 'processing';

      pollingInterval = setInterval(pollRideStatus, 5000);
      pollRideStatus(); // Initial call

      return () => {
        shouldStopPolling = true;
        clearInterval(pollingInterval);

        // Resume global polling when leaving Confirm screen
        resumePolling();
        console.log('[Confirm] Resumed global polling');
      };
    }, [tripStatusManager, pausePolling, resumePolling]),
  );

  // Listen to trip status changes from notifications
  useEffect(() => {
    const unsubscribe = tripStatusManager.addStatusListener((status) => {
      if (status?.status === 'no_drivers_available') {
        console.log('[Confirm] Received no drivers status from notification');
        logAction('Received no drivers status from notification');
        // The TripStatusManager will handle navigation
      }
    });

    return unsubscribe;
  }, [tripStatusManager, logAction]);

  // Clean up driver canceled flag on focus
  useFocusEffect(
    useCallback(() => {
      const cleanup = async () => {
        await AsyncStorage.removeItem('driverCanceled');
      };
      cleanup();
    }, []),
  );

  // Cleanup effect when component unmounts or loses focus
  useEffect(() => {
    return () => {
      // Clear any pending timeouts or intervals when component unmounts
      console.log('[Confirm] Component unmounting, cleaning up...');
    };
  }, []);

  const handleCancel = async () => {
    try {
      const response = await RideService.cancelRide(tripId);
      if (response.status === STATUS_CODE.ok) {
        // Clear all trip-related state
        await tripStatusManager.clearTripStatus();
        await navigationGuard.clearNavigationState();

        console.log('[Confirm] Ride cancelled successfully, navigating to Direction');

        if (await navigationGuard.canNavigate('Direction')) {
          await navigationGuard.startNavigation('Direction');
          navigation.reset({
            index: 0,
            routes: [{name: 'Direction'}],
          });
          setTimeout(() => navigationGuard.completeNavigation(), 500);
        }
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err?.response?.data?.response?.code;

      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        // Even if there's an error, clear the state to allow new rides
        console.log('[Confirm] Server error during cancel, clearing state anyway');
        await tripStatusManager.clearTripStatus();
        await navigationGuard.clearNavigationState();

        if (await navigationGuard.canNavigate('Direction')) {
          await navigationGuard.startNavigation('Direction');
          navigation.reset({
            index: 0,
            routes: [{name: 'Direction'}],
          });
          setTimeout(() => navigationGuard.completeNavigation(), 500);
        }
        return;
      } else if (status === STATUS_CODE.bad_request) {
        if (code === 'trip_already_cancelled') {
          showToast(t('trip_already_cancelled'), 'failure');
          // Trip is already cancelled, clear state
          await tripStatusManager.clearTripStatus();
          await navigationGuard.clearNavigationState();

          if (await navigationGuard.canNavigate('Direction')) {
            await navigationGuard.startNavigation('Direction');
            navigation.reset({
              index: 0,
              routes: [{name: 'Direction'}],
            });
            setTimeout(() => navigationGuard.completeNavigation(), 500);
          }
        }
      }
    }
  };

  if (isLoading) {
    return (
      <ImageBackground source={images.bg2} style={styles.backgroundImage}>
        <SafeAreaView
          style={[
            styles.safeArea,
            {justifyContent: 'center', alignItems: 'center'},
          ]}></SafeAreaView>
      </ImageBackground>
    );
  }

  const sourceAddress =
    pickupAddress ||
    (tripDetails && tripDetails.source_address) ||
    t('loading');
  const destinationAddress =
    whereto || (tripDetails && tripDetails.destination_address) || t('loading');

  return (
    <>
      <ImageBackground source={images.bg2} style={styles.backgroundImage}>
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.locationContainer}>
            <FlexContainer direction="row">
              <View style={{alignItems: 'center'}}>
                <IconSvgView
                  width={16}
                  svgStyle={styles.pickupIcon}
                  source={diamondInactive}
                />
                <View style={{marginBottom: spacing.sm, flex: 1}}>
                  <FadingLine
                    startX={0}
                    startY={1}
                    endX={1}
                    endY={0}
                    width={1}
                    height={'100%'}
                  />
                </View>
                <IconSvgView
                  width={12}
                  svgStyle={styles.dropIcon}
                  source={ellipseActive}
                />
              </View>
              <View style={{marginLeft: spacing.md}}>
                <Text style={styles.locationLabel}>{t('pickup_location')}</Text>
                <Text style={styles.loction} numberOfLines={1}>
                  {sourceAddress}
                </Text>
                <View style={{marginTop: spacing.lg}}>
                  <Text style={styles.locationLabel}>{t('where_to')}</Text>
                  <Text style={styles.loction} numberOfLines={1}>
                    {destinationAddress}
                  </Text>
                </View>
              </View>
            </FlexContainer>
          </View>
        </SafeAreaView>
        <FlexContainer
          direction="column"
          alignItems="center"
          style={styles.loaderContainer}>
          <View style={{width: '100%', alignItems: 'center'}}>
            <View style={{width: '100%'}}>
              <Image
                source={require('../../icons/searching.gif')}
                style={styles.loaderImage}
              />
            </View>
            <Text numberOfLines={2} style={styles.title}>
              {t('finding_drivers_nearby')}
            </Text>
          </View>
        </FlexContainer>
        <Button
          onPress={handleCancel}
          title={t('cancel')}
          style={styles.cancelBtn}
          textColor={colors.red}
        />
      </ImageBackground>
    </>
  );
};

export default Confirm;
