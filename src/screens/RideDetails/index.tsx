import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import MapComponent from '../../components/Map/MapComponent';
import {
  Animated as RNAnimated,
  AppState,
  Easing,
  Image,
  Linking,
  Platform,
  Text,
  TouchableOpacity,
  View,
  StatusBar,
  ScrollView,
} from 'react-native';
import MapView, {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>yline} from 'react-native-maps';
import handler from '../../icons/handler.svg';
import images from '../../constants/images';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import styles from './RideDetailsStyle';
import Button from '../../components/Button/Button';
import {useTranslation} from 'react-i18next';
import {colors, GeistFont} from '../../constants';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';
import {useToast} from '../../components/Toast/Toast';
import pickupIcon from '../../icons/pickupIcon.svg';
import dropIcon from '../../icons/dropIcon.svg';
import {zoomToRoute} from '../../utils/MapUtils';
import myLocation from '../../icons/my_location.svg';
import phone from '../../icons/phone.svg';
import chat from '../../icons/chat.svg';
import {useRideDetails} from '../../hooks/useRideDetails';
import RideService from '../../services/RideService';
import {STATUS_CODE} from '../../constants/constants';
import polyline from '@mapbox/polyline';
import {userLocationContext} from '../../utils/userLocationContext';
import {useFocusEffect} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useUser} from '../../hooks/useUser';
import {formatTime} from '../../utils/TImeUtils';
import ChatModal from '../../components/ChatModal';
import MessageService from '../../services/MessageService';
import BottomSheet, {BottomSheetScrollView} from '@gorhom/bottom-sheet';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  interpolate,
} from 'react-native-reanimated';
import TripService from '../../services/TripService';
import {navigationRef} from '../../router/navigationService';
import {check, PERMISSIONS, request, RESULTS} from 'react-native-permissions';
import {spacing} from '../../constants/theme';
import {SafeAreaView} from 'react-native-safe-area-context';
import autoRickshaw from '../../icons/autoMarker.svg';
import BottomUpModal from '../../components/BottomUpModal/BottomUpModal';
import {max} from 'lodash';

interface RideDetailsScreenProps {
  navigation: any;
  route: any;
}
const RideDetails: React.FC<RideDetailsScreenProps> = ({navigation}) => {
  const {t} = useTranslation();
  const mapViewRef = useRef<MapView>(null);
  const {userLocation} = userLocationContext();
  const {user} = useUser();
  const [address, setAddress] = useState<string>('');
  const [region, setRegion] = useState<LatLng | null>(null);
  const {showToast} = useToast();
  const [routeCoordinates, setRouteCoordinates] = useState<
    {latitude: number; longitude: number}[]
  >([]);
  const [retryCount, setRetryCount] = useState(0);
  const [imageError, setImageError] = useState(false);
  const [hasUnreadMessages, setHasUnreadMessages] = useState(false);
  const badgeOpacity = useRef(new RNAnimated.Value(1)).current;
  const {
    tripDetails,
    setTripDetails,
    driverRoute,
    setDriverRoute,
    timeRemaining,
    setTimeRemaining,
    messages,
    setMessages,
    driverDetails,
    fetchDriverDetails,
  } = useRideDetails();
  const currentTime = new Date().toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
  });
  const [isChatModalVisible, setChatModalVisible] = useState(false);
  const bottomSheetRef = useRef(null);
  const animatedPosition = useSharedValue(1);
  const [otpState, setOtpState] = useState<string[]>([]);
  const [iconLoaded, setIconLoaded] = useState(false);
  const [markerReady, setMarkerReady] = useState(false);
  const [showDriverMovingModal, setShowDriverMovingModal] = useState(false);
  const [markerRotation, setMarkerRotation] = useState<number>(0);

  useEffect(() => {
    fetchTripOtp();
    const checkDriverMoving = async () => {
      const driverStillMoving = await AsyncStorage.getItem('driverStillMoving');
      if (driverStillMoving === 'true') {
        setShowDriverMovingModal(true);
      }
    };

    checkDriverMoving();
  }, []);

  const fetchTripOtp = useCallback(async () => {
    try {
      const response = await TripService.getActiveRide();
      console.log('inside ridedetails', response.data.data);

      if (response.status === STATUS_CODE.ok) {
        if (!response.data.data?.activeRide) {
          clearLocalStorage();
          navigationRef.current?.reset({routes: [{name: 'BottomTab'}]});
        }
        setTripDetails(response.data.data.activeRide);
        if (!response.data.data.activeRide.status == 'verified') {
          await AsyncStorage.setItem('rideStatus', 'ONRIDE');
          navigationRef.current?.reset({routes: [{name: 'RideRoute'}]});
        }
        if (response.data.data?.activeRide?.otp) {
          if (typeof response.data.data.activeRide.otp === 'string') {
            setOtpState([...response.data.data.activeRide.otp.split('')]);
          } else if (Array.isArray(response.data.data.activeRide.otp)) {
            setOtpState([...response.data.data.activeRide.otp]);
          }
        }
        const timer = setTimeout(() => {
          setMarkerReady(true);
        }, 1500);
      }
    } catch (err) {
      console.log('Error fetching trip details:', err);
    }
  }, []);

  useEffect(() => {
    const handleAppStateChange = async () => {
      const storedTripId = await AsyncStorage.getItem('tripId');
      const rideStatus = await AsyncStorage.getItem('rideStatus');
      const driverCanceled = await AsyncStorage.getItem('driverCanceled');
      const polyline = await AsyncStorage.getItem('locationUpdate');
      const arrived = await AsyncStorage.getItem('driverArrived');
      const nearby = await AsyncStorage.getItem('driverNearby');
      const rideAborted = await AsyncStorage.getItem('rideAborted');
      const driverStillMoving = await AsyncStorage.getItem('driverStillMoving');

      if (nearby) {
        await AsyncStorage.removeItem('driverNearby');
        showToast(t('driver_nearby'), 'success');
      } else if (arrived) {
        await AsyncStorage.removeItem('driverNearby');
        await AsyncStorage.removeItem('driverArrived');
        showToast(t('driver_arrived'), 'success');
      } else if (rideAborted) {
        showToast(t('ride_aborted'), 'failure');
        await clearLocalStorage();
        navigationRef.current?.reset({routes: [{name: 'BottomTab'}]});
      }
      if (rideStatus === 'ONRIDE') {
        if (storedTripId) {
          await fetchDriverDetails(JSON.parse(storedTripId));
          await fetchTripOtp(storedTripId);
        }
        navigationRef.current?.reset({routes: [{name: 'RideRoute'}]});
      } else if (rideStatus === 'COMPLETED') {
        if (storedTripId) {
          await fetchDriverDetails(JSON.parse(storedTripId));
          await fetchTripOtp(storedTripId);
        }
        navigationRef.current?.reset({routes: [{name: 'CollectCash'}]});
      } else if (driverCanceled) {
        showToast(t('driver_cancel'), 'failure');
        await clearLocalStorage();
        await AsyncStorage.removeItem('rideStatus');
        navigationRef.current?.reset({routes: [{name: 'Confirm'}]});
      } else if (polyline) {
        setDriverRoute(polyline);
      }

      if (driverStillMoving === 'true') {
        setShowDriverMovingModal(true);
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => {
      subscription.remove();
    };
  }, [fetchTripOtp]);

  const handleCloseDriverMovingModal = async () => {
    await AsyncStorage.removeItem('driverStillMoving');
    setShowDriverMovingModal(false);
  };

  const handleSheetChanges = useCallback(
    (index: React.SetStateAction<number>) => {
      StatusBar.setBarStyle('light-content');
      StatusBar.setBackgroundColor('transparent');
      StatusBar.setTranslucent(true);

      animatedPosition.value = withSpring(Number(index));
    },
    [],
  );

  const mapContainerStyle = useAnimatedStyle(() => {
    const flex = interpolate(
      animatedPosition.value,
      [0, 1],
      Platform.OS === 'ios' ? [0.78, 0.36] : [0.75, 0.35],
    );
    return {
      flex,
      position: 'relative',
      width: '100%',
    };
  });

  const locationIconsStyle = useAnimatedStyle(() => {
    const bottomPosition = interpolate(animatedPosition.value, [0, 1], [10, 5]);

    return {
      position: 'absolute',
      right: 16,
      bottom: `${bottomPosition}%`,
      zIndex: 1,
      flexDirection: 'column',
      gap: 8,
    };
  });
  const snapPoints = useMemo(() => ['25%', '65%'], []);

  const sendMessage = async (message: string) => {
    if (!message.trim() || !tripDetails?.id) return;

    console.log(message, tripDetails.id, 'message');

    try {
      const response = await MessageService.sendMessages(
        message,
        Number(tripDetails.id),
      );
      console.log(response, 'response');
      if (response.status === STATUS_CODE.created) {
        setMessages(prevMessages => [
          ...prevMessages,
          {
            id: Date.now(),
            text: message,
            sender: true,
            time: currentTime,
            content: message,
          },
        ]);
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  useFocusEffect(
    useCallback(() => {
      if (routeCoordinates.length > 0) {
        const interval = setInterval(() => {
          if (mapViewRef.current) {
            zoomToRoute(mapViewRef, routeCoordinates);
            clearInterval(interval);
          } else {
            console.log('mapViewRef is not set');
          }
        }, 500);

        return () => clearInterval(interval);
      }
    }, [routeCoordinates, mapViewRef.current]),
  );

  useFocusEffect(
    useCallback(() => {
      const checkArrivals = async () => {
        const arrived = await AsyncStorage.getItem('driverArrived');
        const nearby = await AsyncStorage.getItem('driverNearby');

        if (nearby) {
          await AsyncStorage.removeItem('driverNearby');
          showToast(t('driver_nearby'), 'success');
        } else if (arrived) {
          await AsyncStorage.removeItem('driverNearby');
          await AsyncStorage.removeItem('driverArrived');
          showToast(t('driver_arrived'), 'success');
        }
      };

      const fetchDetails = async () => {
        const tripId = await AsyncStorage.getItem('tripId');
        if (tripId) {
          await fetchDriverDetails(JSON.parse(tripId));
          await fetchTripOtp(tripId);
        }

        if (tripDetails?.id) {
          try {
            const response = await RideService.getPolyline(
              Number(tripDetails.id),
            );
            if (response.status === STATUS_CODE.ok) {
              setDriverRoute(response.data.data.route);
              setTimeRemaining(response.data.data.remainingTime);
            }
          } catch (err: any) {
            const status = err?.response?.status;
            const code = err?.response?.data?.response?.code;

            if (
              [STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)
            ) {
              return;
            } else if (STATUS_CODE.bad_request) {
              code === 'driver_location_not_found' &&
                showToast(t('driver_location_not_found'), 'failure');
            }
          }
        }
      };

      checkArrivals();
      fetchDetails();
    }, []),
  );

  useEffect(() => {
    const computeRotation = (
      coords: {latitude: number; longitude: number}[],
    ): number => {
      if (coords.length < 2) return 0;

      const point1 = coords[0];
      const point2 = coords[1];

      const dx = point2.longitude - point1.longitude;
      const dy = point2.latitude - point1.latitude;

      let angle = (Math.atan2(dx, dy) * 180) / Math.PI;

      return angle + 180;
    };

    if (driverRoute && typeof driverRoute === 'string') {
      try {
        const decodedCoordinates = polyline
          .decode(driverRoute)
          .map(([latitude, longitude]) => ({
            latitude: Number(latitude),
            longitude: Number(longitude),
          }));
        setRouteCoordinates(decodedCoordinates);

        if (decodedCoordinates.length >= 2) {
          setMarkerRotation(computeRotation(decodedCoordinates));
        }
      } catch (error) {
        console.error('Error decoding route:', error);
      }
    }
  }, [driverRoute]);

  useEffect(() => {
    if (tripDetails?.otp) {
      if (typeof tripDetails.otp === 'string') {
        setOtpState([...tripDetails.otp.split('')]);
      } else if (Array.isArray(tripDetails.otp)) {
        setOtpState([...tripDetails.otp]);
      } else {
        setOtpState([]);
      }
    } else {
      setOtpState([]);
    }
  }, [tripDetails?.otp]);

  const renderedNumbers =
    otpState.length > 0 ? (
      otpState.map((item: string, index: number) => (
        <View key={index} style={styles.pinCard}>
          <Text style={styles.pinNumber}>{item}</Text>
        </View>
      ))
    ) : (
      <Text></Text>
    );

  // const toggleChatModal = async () => {
  //   if (!isChatModalVisible) {
  //     setHasUnreadMessages(false);
  //     await fetchMessages(true);
  //   }
  //   setChatModalVisible(!isChatModalVisible);
  // };

  // const fetchMessages = async (isInitialLoad = false) => {
  //   if (!tripDetails?.id) return;

  //   try {
  //     const response = await MessageService.getMessages(Number(tripDetails.id));

  //     if (response.status === 200) {
  //       const newMessages = response.data.data.map((msg: any) => {
  //         const time = msg.timestamp
  //           ? formatTime(new Date(msg.timestamp))
  //           : 'Unknown Time';

  //         return {
  //           id: msg.id,
  //           text: msg.content,
  //           sender: msg.userId !== null,
  //           time: time,
  //           content: msg.content,
  //         };
  //       });

  //       if (
  //         !isChatModalVisible &&
  //         messages.length > 0 &&
  //         newMessages.length > messages.length &&
  //         !isInitialLoad
  //       ) {
  //         const hasNewUserMessage = newMessages.some(
  //           (newMsg: {sender: boolean; id: number}) =>
  //             !newMsg.sender &&
  //             !messages.some(oldMsg => oldMsg.id === newMsg.id),
  //         );
  //         if (hasNewUserMessage) {
  //           setHasUnreadMessages(true);
  //         }
  //       }

  //       setMessages(newMessages);
  //     }
  //   } catch (error) {
  //     console.log('Error fetching messages:', error);
  //   }
  // };

  // useEffect(() => {
  //   if (tripDetails?.id) {
  //     fetchMessages();
  //     const interval = setInterval(
  //       () => {
  //         fetchMessages();
  //       },
  //       isChatModalVisible ? 2000 : 5000,
  //     );

  //     return () => clearInterval(interval);
  //   }
  // }, [tripDetails, isChatModalVisible]);

  // useEffect(() => {
  //   let flickerAnimation: RNAnimated.CompositeAnimation;

  //   if (hasUnreadMessages && !isChatModalVisible) {
  //     flickerAnimation = RNAnimated.loop(
  //       RNAnimated.sequence([
  //         RNAnimated.timing(badgeOpacity, {
  //           toValue: 0.3,
  //           duration: 500,
  //           easing: Easing.ease,
  //           useNativeDriver: true,
  //         }),
  //         RNAnimated.timing(badgeOpacity, {
  //           toValue: 1,
  //           duration: 500,
  //           easing: Easing.ease,
  //           useNativeDriver: true,
  //         }),
  //       ]),
  //     );

  //     flickerAnimation.start();
  //   } else {
  //     badgeOpacity.setValue(1);
  //   }

  //   return () => {
  //     if (flickerAnimation) {
  //       flickerAnimation.stop();
  //     }
  //   };
  // }, [hasUnreadMessages, isChatModalVisible, badgeOpacity]);

  const clearLocalStorage = async () => {
    setTripDetails(null);
    await AsyncStorage.multiRemove([
      'tripId',
      'rideStatus',
      'noDrivers',
      'driverCanceled',
      'rideAborted',
      'driverArrived',
      'driverNearby',
    ]);
  };

  const handleCancel = async (e: any) => {
    try {
      const response = await RideService.cancelRide();
      if (response.status === STATUS_CODE.ok) {
        await clearLocalStorage();
        navigation?.reset({routes: [{name: 'Direction'}]});
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err?.response?.code;

      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      } else if (STATUS_CODE.bad_request) {
        code === 'trip_already_cancelled' &&
          showToast(t('trip_already_cancelled'), 'failure');
      }
    }
  };

  const handleGetLocation = () => {
    if (userLocation) {
      const {latitude, longitude} = userLocation;
      const newRegion = {
        latitude,
        longitude,
        latitudeDelta: 0.008,
        longitudeDelta: 0.008,
      };
      AsyncStorage.setItem('lastMapRegion', JSON.stringify(newRegion));

      mapViewRef.current?.animateToRegion(newRegion, 100);
    }
  };

  const handleCallDriver = async () => {
    const phoneNumber = driverDetails?.phone;
    if (!phoneNumber) {
      showToast(t('driver_number_not_available'));
      return;
    }

    if (Platform.OS === 'ios') {
      makePhoneCall(phoneNumber);
      return;
    }

    try {
      const result = await check(PERMISSIONS.ANDROID.CALL_PHONE);

      switch (result) {
        case RESULTS.GRANTED:
          makePhoneCall(phoneNumber);
          break;
        case RESULTS.DENIED:
          const requestResult = await request(PERMISSIONS.ANDROID.CALL_PHONE);
          if (requestResult === RESULTS.GRANTED) {
            makePhoneCall(phoneNumber);
          } else {
            showToast(t('call_permission_denied'));
          }
          break;
        case RESULTS.BLOCKED:
          showToast(t('call_permission_blocked'));
          break;
        default:
          showToast(t('unable_make_call'));
      }
    } catch (err) {
      console.error('Error checking permission:', err);
      showToast(t('unable_make_call'));
    }
  };

  const makePhoneCall = (phoneNumber: string) => {
    Linking.openURL(`tel:${phoneNumber}`)
      .then(() => console.log('Dialer opened successfully'))
      .catch(err => {
        console.error('Error opening dialer:', err);
        showToast(t('unable_make_call'));
      });
  };

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (tripDetails?.id) {
      interval = setInterval(async () => {
        try {
          const response = await RideService.getPolyline();
          if (response.status === STATUS_CODE.ok) {
            setDriverRoute(response.data.data.route);
            setTimeRemaining(response.data.data.remainingTime);
          }
        } catch (err) {
          console.log('Error polling polyline:', err);
        }
      }, 10000);
    }
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [tripDetails?.id]);

  useEffect(() => {
    if (driverRoute) {
      setIconLoaded(false);
      setMarkerReady(false);

      setTimeout(() => {
        setRetryCount(prev => prev + 1);
        setMarkerReady(true);
      }, 200);
    }
  }, [driverRoute]);

  const calculateRotation = useCallback(
    (coords: {latitude: number; longitude: number}[]) => {
      if (coords.length < 2) return 0;

      const point1 = coords[0];
      const point2 = coords[1];

      const dx = point2.longitude - point1.longitude;
      const dy = point2.latitude - point1.latitude;

      let angle = (Math.atan2(dx, dy) * 180) / Math.PI;

      return angle + 180;
    },
    [],
  );

  useEffect(() => {
    if (driverRoute && typeof driverRoute === 'string') {
      try {
        const decodedCoordinates = polyline
          .decode(driverRoute)
          .map(([latitude, longitude]) => ({
            latitude: Number(latitude),
            longitude: Number(longitude),
          }));

        setRouteCoordinates(decodedCoordinates);

        if (decodedCoordinates.length >= 2) {
          setMarkerRotation(calculateRotation(decodedCoordinates));
        }
      } catch (error) {
        console.error('Error decoding route:', error);
      }
    }
  }, [driverRoute, calculateRotation]);

  return (
    <View
      style={{
        backgroundColor: colors.black,
        flex: 1,
        position: 'relative',
      }}>
      <BottomUpModal
        showModal={showDriverMovingModal}
        onClose={handleCloseDriverMovingModal}
        title={t('driver_status')}
        description={t('driver_moving')}
        buttonText={t('ok')}
        onButtonClick={handleCloseDriverMovingModal}
        forceUpdate={false}
      />
      <StatusBar
        translucent
        backgroundColor="transparent"
        barStyle="light-content"
      />
      <SafeAreaView
        style={{
          flex: 1,
          backgroundColor: colors.black,
        }}
        edges={['bottom']}>
        <Animated.View style={mapContainerStyle}>
          <MapComponent
            ref={mapViewRef}
            marker={false}
            setAddress={setAddress}
            region={region}
            setRegion={setRegion}
            style={{flex: 1, minHeight: 280}}>
            {routeCoordinates.length === 0 && (
              <View
                style={{width: '100%', height: '100%', position: 'absolute'}}
              />
            )}

            {routeCoordinates[routeCoordinates.length - 1] && (
              <>
                {routeCoordinates[0] && (
                  <Marker
                    key={`pickup-${retryCount}`}
                    anchor={{x: 0.5, y: 0.5}}
                    tracksViewChanges={false}
                    coordinate={routeCoordinates[routeCoordinates.length - 1]}
                    title="Pickup Location">
                    <IconSvgView size={25} source={pickupIcon} />
                  </Marker>
                )}

                {routeCoordinates.length > 0 && (
                  <Marker
                    key={`drop-${retryCount}`}
                    anchor={{x: 0.5, y: 0.5}}
                    tracksViewChanges={!markerReady || !iconLoaded}
                    coordinate={routeCoordinates[0]}
                    title="Drop Location">
                    <View
                      onLayout={() => {
                        setTimeout(() => setIconLoaded(true), 300);
                      }}>
                      {driverDetails?.vehicle?.vehicle_type ===
                      'AUTORICKSHAW' ? (
                        <Image
                          source={images.autoTop}
                          style={{
                            width: 60,
                            height: 60,
                            transform: [{rotate: `${markerRotation}deg`}],
                          }}
                          resizeMode="contain"
                        />
                      ) : (
                        <IconSvgView size={30} source={dropIcon} />
                      )}
                    </View>
                  </Marker>
                )}

                {routeCoordinates.length > 1 && (
                  <Polyline
                    coordinates={routeCoordinates}
                    strokeColor="#FFFFFF"
                    strokeColors={['#FFFFFF']}
                    strokeWidth={4}
                  />
                )}
              </>
            )}
          </MapComponent>
          <Animated.View style={locationIconsStyle}>
            <TouchableOpacity
              style={styles.locationContainer}
              onPress={handleGetLocation}>
              <IconSvgView width={24} source={myLocation} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.locationContainer}
              onPress={handleCallDriver}>
              <IconSvgView width={24} source={phone} />
            </TouchableOpacity>
            {/* <TouchableOpacity
              onPress={toggleChatModal}
              style={styles.locationContainer}>
              <IconSvgView width={24} source={chat} />
              {hasUnreadMessages && !isChatModalVisible && (
                <RNAnimated.View
                  style={{
                    position: 'absolute',
                    top: -5,
                    right: -5,
                    backgroundColor: colors.lightGreen,
                    width: spacing.md,
                    height: spacing.md,
                    borderRadius: spacing.md,
                    borderWidth: 1,
                    borderColor: colors.white,
                    opacity: badgeOpacity,
                  }}
                />
              )}
            </TouchableOpacity> */}
          </Animated.View>
        </Animated.View>

        <BottomSheet
          ref={bottomSheetRef}
          index={1}
          snapPoints={snapPoints}
          onChange={handleSheetChanges}
          enableOverDrag={false}
          handleComponent={() => (
            <View style={{backgroundColor: colors.black, alignItems: 'center'}}>
              <IconSvgView width={60} source={handler} />
            </View>
          )}>
          <BottomSheetScrollView
            contentContainerStyle={styles.scrollViewContent}
            showsVerticalScrollIndicator={false}>
            <View style={styles.safeArea}>
              {!tripDetails || !driverDetails ? (
                <View
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: colors.black,
                    height: 400,
                  }}>
                  <Text style={{color: colors.white}}>{t('loading')}</Text>
                </View>
              ) : (
                <>
                  <Text numberOfLines={1} style={styles.arriveTxt}>
                    {t('arriving_in')} {Math.round(timeRemaining / 60)}{' '}
                    {t('min')}
                  </Text>
                  <FlexContainer
                    styles={{
                      marginVertical: spacing.md,
                      minHeight: 50,
                      maxHeight: 50,
                    }}
                    flex={0}
                    direction="row"
                    justifyContent="space-between"
                    alignItems="center">
                    <Text numberOfLines={2} style={styles.pinTxt}>
                      {t('pin_for_this_ride')}
                    </Text>
                    <FlexContainer direction="row" justifyContent="flex-end">
                      {renderedNumbers}
                    </FlexContainer>
                  </FlexContainer>
                  <FadingHorizontalLine />
                  <View
                    style={{
                      minHeight: 100,
                      maxHeight: 100,
                      paddingBottom: spacing.md,
                      flexDirection: 'row',
                    }}>
                    <View style={styles.vehicleContainer}>
                      <Text style={styles.makeTxt}>
                        {driverDetails?.vehicle?.vehicle_type === 'AUTORICKSHAW'
                          ? t('autoRickshaw')
                          : driverDetails?.vehicle?.vehicle_type}
                      </Text>
                      <FlexContainer direction="row" alignItems="center">
                        {driverDetails?.vehicle?.vehicle_type ===
                        'AUTORICKSHAW' ? (
                          <Image source={images.auto} style={styles.autoIcon} />
                        ) : driverDetails?.vehicle?.vehicle_type === 'CAR' ? (
                          <Image source={images.car} />
                        ) : null}
                        <Text style={styles.vehicleNo}>
                          {driverDetails?.vehicle?.vehicle_no}
                        </Text>
                      </FlexContainer>
                    </View>

                    <View
                      style={{
                        marginLeft: spacing.lg,
                        flex: 1,
                        alignSelf: 'flex-end',
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'flex-end',
                      }}>
                      <Image
                        source={
                          imageError || !driverDetails?.profile_photo
                            ? images.user
                            : {uri: driverDetails?.profile_photo}
                        }
                        style={styles.imageContainer}
                        resizeMode="cover"
                        onError={() => setImageError(true)}
                      />
                      <View
                        style={{
                          marginLeft: spacing.sm,
                          justifyContent: 'center',
                        }}>
                        <Text
                          numberOfLines={2}
                          style={[
                            styles.driverName,
                            {
                              flexWrap: 'wrap',
                              maxWidth: 120,
                              textAlignVertical: 'center',
                            },
                          ]}>
                          {driverDetails?.name}
                        </Text>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            marginTop: 4,
                          }}>
                          {driverDetails?.average_rating === 0 ? (
                            <Text
                              style={{
                                color: 'white',
                                fontSize: 14,
                                fontFamily: GeistFont.regular,
                              }}>
                              {t('no_rating', 'No rating')}
                            </Text>
                          ) : (
                            <>
                              <Image
                                source={images.star}
                                style={{
                                  width: 16,
                                  height: 16,
                                }}
                              />
                              <Text
                                style={{
                                  color: 'white',
                                  fontSize: 14,
                                  marginLeft: 5,
                                  fontFamily: GeistFont.regular,
                                }}>
                                {driverDetails?.average_rating}
                              </Text>
                            </>
                          )}
                        </View>
                      </View>
                    </View>
                  </View>
                  <FadingHorizontalLine />
                  <FlexContainer
                    styles={{
                      minHeight: 80,
                      maxHeight: 80,
                    }}
                    flex={0}
                    direction="row"
                    justifyContent="space-between"
                    alignItems="center">
                    <View>
                      <Text style={[styles.arriveTxt, {textAlign: 'left'}]}>
                        {t('ride_fare')}
                      </Text>
                      <Text style={styles.rateTxt}>₹{tripDetails?.fare}</Text>
                    </View>
                    <View style={{maxWidth: '50%'}}>
                      <Text numberOfLines={2} style={styles.arriveTxt}>
                        {t('cash_upi_app')}
                      </Text>
                    </View>
                  </FlexContainer>
                  <FadingHorizontalLine />
                  <FlexContainer
                    styles={{
                      marginBottom: 20,
                      minHeight: 100,
                      maxHeight: 100,
                    }}
                    flex={0}>
                    <Text style={styles.locationLabel}>
                      {t('pickup_location')}
                    </Text>
                    <Text style={styles.location} numberOfLines={1}>
                      {tripDetails?.source_address}
                    </Text>
                    <Text style={styles.locationLabel}>{t('where_to')}</Text>
                    <Text style={styles.location} numberOfLines={1}>
                      {tripDetails?.destination_address}
                    </Text>
                  </FlexContainer>
                  <Button
                    onPress={handleCancel}
                    title={t('cancel_ride')}
                    style={styles.cancelBtn}
                    textColor={colors.red}
                  />
                </>
              )}
            </View>
          </BottomSheetScrollView>
        </BottomSheet>
        {isChatModalVisible && (
          <ChatModal
            visible={isChatModalVisible}
            onClose={() => setChatModalVisible(false)}
            userId={user?.id.toString()!}
            messages={messages}
            tripId={tripDetails?.id}
            sendMessage={sendMessage}
            isLoading={false}
          />
        )}
      </SafeAreaView>
    </View>
  );
};

export default RideDetails;
