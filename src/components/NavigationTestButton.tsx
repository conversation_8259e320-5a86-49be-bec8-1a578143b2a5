import React from 'react';
import { TouchableOpacity, Text, StyleSheet, Alert } from 'react-native';
import TripStatusManager from '../utils/TripStatusManager';
import NavigationGuard from '../utils/NavigationGuard';
import DebugNavigationState from '../utils/DebugNavigationState';

interface NavigationTestButtonProps {
  onPress?: () => void;
}

/**
 * Temporary test button to verify navigation fixes
 * Add this to Direction screen to test the fix
 */
const NavigationTestButton: React.FC<NavigationTestButtonProps> = ({ onPress }) => {
  const tripStatusManager = TripStatusManager.getInstance();
  const navigationGuard = NavigationGuard.getInstance();
  const debugState = DebugNavigationState.getInstance();

  const runQuickTest = async () => {
    try {
      console.log('🧪 [TestButton] Starting quick navigation test...');
      
      // Step 1: Check current state
      const currentState = await debugState.getCurrentState();
      console.log('🧪 [TestButton] Current state:', currentState);
      
      // Step 2: Force reset everything
      await tripStatusManager.forceResetForNewRide();
      console.log('🧪 [TestButton] Force reset completed');
      
      // Step 3: Check if ready for new ride
      const isReady = await tripStatusManager.isReadyForNewRide();
      console.log('🧪 [TestButton] Ready for new ride:', isReady);
      
      // Step 4: Simulate the problematic scenario
      console.log('🧪 [TestButton] Simulating: processing -> accepted -> driver_cancelled -> no_drivers');
      
      await tripStatusManager.updateTripStatus('processing', 'test-1', false);
      await new Promise(resolve => setTimeout(resolve, 500));
      
      await tripStatusManager.updateTripStatus('accepted', 'test-1', false);
      await new Promise(resolve => setTimeout(resolve, 500));
      
      await tripStatusManager.handleDriverCancelled();
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      await tripStatusManager.handleNoDriversAvailable();
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Step 5: Check if ready for new ride after the scenario
      const isReadyAfterScenario = await tripStatusManager.isReadyForNewRide();
      console.log('🧪 [TestButton] Ready after scenario:', isReadyAfterScenario);
      
      // Step 6: Try to create new ride
      if (isReadyAfterScenario) {
        await tripStatusManager.updateTripStatus('processing', 'test-2', false);
        console.log('🧪 [TestButton] ✅ New ride created successfully!');
        
        Alert.alert(
          'Test Result', 
          '✅ SUCCESS: New ride can be created after driver cancel + no drivers scenario!',
          [{ text: 'OK' }]
        );
      } else {
        console.log('🧪 [TestButton] ❌ Cannot create new ride');
        
        Alert.alert(
          'Test Result', 
          '❌ FAILED: Cannot create new ride after scenario. Check console logs.',
          [{ text: 'OK' }]
        );
      }
      
      // Step 7: Final state check
      const finalState = await debugState.getCurrentState();
      console.log('🧪 [TestButton] Final state:', finalState);
      
    } catch (error) {
      console.error('🧪 [TestButton] Test failed:', error);
      Alert.alert('Test Error', `Test failed: ${error.message}`);
    }
  };

  const checkCurrentState = async () => {
    const state = await debugState.getCurrentState();
    const isReady = await tripStatusManager.isReadyForNewRide();
    
    Alert.alert(
      'Current State',
      `Ready for new ride: ${isReady}\nTrip Status: ${state.tripStatusManager.currentStatus?.status || 'None'}\nCheck console for full details`,
      [{ text: 'OK' }]
    );
  };

  const forceReset = async () => {
    await tripStatusManager.forceResetForNewRide();
    Alert.alert('Reset Complete', 'All state has been reset. App should be ready for new rides.');
  };

  return (
    <>
      <TouchableOpacity style={styles.button} onPress={runQuickTest}>
        <Text style={styles.buttonText}>🧪 Test Navigation Fix</Text>
      </TouchableOpacity>
      
      <TouchableOpacity style={[styles.button, styles.secondaryButton]} onPress={checkCurrentState}>
        <Text style={styles.buttonText}>📊 Check State</Text>
      </TouchableOpacity>
      
      <TouchableOpacity style={[styles.button, styles.resetButton]} onPress={forceReset}>
        <Text style={styles.buttonText}>🔄 Force Reset</Text>
      </TouchableOpacity>
    </>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
    margin: 4,
    alignItems: 'center',
  },
  secondaryButton: {
    backgroundColor: '#34C759',
  },
  resetButton: {
    backgroundColor: '#FF3B30',
  },
  buttonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default NavigationTestButton;
