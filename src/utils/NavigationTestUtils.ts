import AsyncStorage from '@react-native-async-storage/async-storage';
import TripStatusManager from './TripStatusManager';
import NavigationGuard from './NavigationGuard';

/**
 * Test utilities to verify navigation fixes
 */
export class NavigationTestUtils {
  private static instance: NavigationTestUtils;
  private tripStatusManager = TripStatusManager.getInstance();
  private navigationGuard = NavigationGuard.getInstance();

  private constructor() {}

  public static getInstance(): NavigationTestUtils {
    if (!NavigationTestUtils.instance) {
      NavigationTestUtils.instance = new NavigationTestUtils();
    }
    return NavigationTestUtils.instance;
  }

  /**
   * Test scenario: No drivers available
   */
  public async testNoDriversScenario(): Promise<boolean> {
    try {
      console.log('[Test] Testing no drivers scenario...');
      
      // Simulate no drivers available
      await this.tripStatusManager.handleNoDriversAvailable();
      
      // Check if flag is set correctly
      const isNoDrivers = await this.tripStatusManager.isNoDriversScenario();
      
      if (!isNoDrivers) {
        console.error('[Test] No drivers flag not set correctly');
        return false;
      }
      
      // Clear the flag
      await this.tripStatusManager.clearNoDriversFlag();
      
      // Verify flag is cleared
      const isClearedNoDrivers = await this.tripStatusManager.isNoDriversScenario();
      
      if (isClearedNoDrivers) {
        console.error('[Test] No drivers flag not cleared correctly');
        return false;
      }
      
      console.log('[Test] No drivers scenario test passed ✅');
      return true;
    } catch (error) {
      console.error('[Test] No drivers scenario test failed:', error);
      return false;
    }
  }

  /**
   * Test scenario: Navigation guard prevents multiple navigations
   */
  public async testNavigationGuard(): Promise<boolean> {
    try {
      console.log('[Test] Testing navigation guard...');
      
      // Clear any existing navigation state
      await this.navigationGuard.clearNavigationState();
      
      // First navigation should be allowed
      const canNavigate1 = await this.navigationGuard.canNavigate('Direction');
      if (!canNavigate1) {
        console.error('[Test] First navigation should be allowed');
        return false;
      }
      
      // Start navigation
      await this.navigationGuard.startNavigation('Direction');
      
      // Second navigation should be blocked
      const canNavigate2 = await this.navigationGuard.canNavigate('Confirm');
      if (canNavigate2) {
        console.error('[Test] Second navigation should be blocked');
        return false;
      }
      
      // Complete navigation
      await this.navigationGuard.completeNavigation();
      
      // Third navigation should be allowed after completion
      const canNavigate3 = await this.navigationGuard.canNavigate('Confirm');
      if (!canNavigate3) {
        console.error('[Test] Navigation should be allowed after completion');
        return false;
      }
      
      console.log('[Test] Navigation guard test passed ✅');
      return true;
    } catch (error) {
      console.error('[Test] Navigation guard test failed:', error);
      return false;
    }
  }

  /**
   * Test scenario: Driver cancellation
   */
  public async testDriverCancellation(): Promise<boolean> {
    try {
      console.log('[Test] Testing driver cancellation...');
      
      // Simulate driver cancellation
      await this.tripStatusManager.handleDriverCancelled();
      
      // Check current status
      const currentStatus = this.tripStatusManager.getCurrentStatus();
      
      if (!currentStatus || currentStatus.status !== 'driver_cancelled') {
        console.error('[Test] Driver cancelled status not set correctly');
        return false;
      }
      
      console.log('[Test] Driver cancellation test passed ✅');
      return true;
    } catch (error) {
      console.error('[Test] Driver cancellation test failed:', error);
      return false;
    }
  }

  /**
   * Test scenario: Trip status updates
   */
  public async testTripStatusUpdates(): Promise<boolean> {
    try {
      console.log('[Test] Testing trip status updates...');
      
      const testStatuses = ['processing', 'accepted', 'verified', 'completed'];
      
      for (const status of testStatuses) {
        await this.tripStatusManager.updateTripStatus(status, 'test-trip-id');
        
        const currentStatus = this.tripStatusManager.getCurrentStatus();
        
        if (!currentStatus || currentStatus.status !== status) {
          console.error(`[Test] Status ${status} not set correctly`);
          return false;
        }
      }
      
      console.log('[Test] Trip status updates test passed ✅');
      return true;
    } catch (error) {
      console.error('[Test] Trip status updates test failed:', error);
      return false;
    }
  }

  /**
   * Test scenario: Driver accepts then cancels, then no drivers, then new ride
   */
  public async testDriverCancelThenNoDriversScenario(): Promise<boolean> {
    try {
      console.log('[Test] Testing driver cancel -> no drivers -> new ride scenario...');

      // 1. Start with processing status
      await this.tripStatusManager.updateTripStatus('processing', 'test-trip-1');

      // 2. Driver accepts
      await this.tripStatusManager.updateTripStatus('accepted', 'test-trip-1');

      // 3. Driver cancels
      await this.tripStatusManager.handleDriverCancelled();

      // 4. No drivers available
      await this.tripStatusManager.handleNoDriversAvailable();

      // 5. Check if ready for new ride
      const isReady = await this.tripStatusManager.isReadyForNewRide();

      if (!isReady) {
        console.error('[Test] Should be ready for new ride after driver cancel + no drivers');
        return false;
      }

      // 6. Try to create new ride
      await this.tripStatusManager.updateTripStatus('processing', 'test-trip-2');

      const currentStatus = this.tripStatusManager.getCurrentStatus();
      if (!currentStatus || currentStatus.status !== 'processing' || currentStatus.tripId !== 'test-trip-2') {
        console.error('[Test] New ride should be created successfully');
        return false;
      }

      console.log('[Test] Driver cancel -> no drivers -> new ride test passed ✅');
      return true;
    } catch (error) {
      console.error('[Test] Driver cancel -> no drivers -> new ride test failed:', error);
      return false;
    }
  }

  /**
   * Run all tests
   */
  public async runAllTests(): Promise<boolean> {
    console.log('[Test] Starting navigation tests...');

    const tests = [
      this.testNoDriversScenario(),
      this.testNavigationGuard(),
      this.testDriverCancellation(),
      this.testTripStatusUpdates(),
      this.testDriverCancelThenNoDriversScenario(),
    ];

    const results = await Promise.all(tests);
    const allPassed = results.every(result => result);

    if (allPassed) {
      console.log('[Test] All navigation tests passed! 🎉');
    } else {
      console.error('[Test] Some navigation tests failed! ❌');
    }

    return allPassed;
  }

  /**
   * Clean up test data
   */
  public async cleanup(): Promise<void> {
    await this.tripStatusManager.clearTripStatus();
    await this.navigationGuard.clearNavigationState();
    console.log('[Test] Test cleanup completed');
  }
}

export default NavigationTestUtils;
