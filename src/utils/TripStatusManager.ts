import AsyncStorage from '@react-native-async-storage/async-storage';
import NavigationGuard from './NavigationGuard';
import { navigationRef } from '../router/navigationService';

export interface TripStatus {
  status: string;
  tripId?: string;
  timestamp: number;
}

class TripStatusManager {
  private static instance: TripStatusManager;
  private currentStatus: TripStatus | null = null;
  private statusListeners: ((status: TripStatus | null) => void)[] = [];
  private navigationGuard = NavigationGuard.getInstance();

  private constructor() {}

  public static getInstance(): TripStatusManager {
    if (!TripStatusManager.instance) {
      TripStatusManager.instance = new TripStatusManager();
    }
    return TripStatusManager.instance;
  }

  public async updateTripStatus(status: string, tripId?: string, autoNavigate: boolean = true): Promise<void> {
    const newStatus: TripStatus = {
      status,
      tripId,
      timestamp: Date.now(),
    };

    this.currentStatus = newStatus;

    // Store in AsyncStorage
    await AsyncStorage.setItem('tripStatus', JSON.stringify(newStatus));

    // Notify listeners
    this.statusListeners.forEach(listener => listener(newStatus));

    console.log(`[TripStatusManager] Status updated to: ${status}, autoNavigate: ${autoNavigate}`);

    // Handle navigation based on status only if autoNavigate is true
    if (autoNavigate) {
      await this.handleStatusNavigation(newStatus);
    }
  }

  private async handleStatusNavigation(status: TripStatus): Promise<void> {
    const navigationRoutes: Record<string, string> = {
      processing: 'Confirm',
      accepted: 'RideDetails',
      verified: 'RideRoute',
      completed: 'CollectCash',
      aborted: 'BottomTab',
      no_drivers_available: 'Direction',
      driver_cancelled: 'Direction',
    };

    const targetRoute = navigationRoutes[status.status];

    if (targetRoute && await this.navigationGuard.canNavigate(targetRoute)) {
      await this.navigationGuard.startNavigation(targetRoute);

      try {
        navigationRef.current?.reset({
          index: 0,
          routes: [{ name: targetRoute }],
        });

        // Complete navigation after a short delay
        setTimeout(async () => {
          await this.navigationGuard.completeNavigation();

          // Special handling AFTER navigation for terminal statuses
          if (status.status === 'driver_cancelled' || status.status === 'no_drivers_available') {
            // Clear trip status completely to allow new ride creation
            // Do this after navigation to avoid interfering with the navigation flow
            setTimeout(async () => {
              await this.clearTripStatusForNewRide();
              console.log(`[TripStatusManager] Cleared trip status for ${status.status}, ready for new ride`);
            }, 1000);
          }
        }, 500);

      } catch (error) {
        console.error(`[TripStatusManager] Navigation error:`, error);
        await this.navigationGuard.completeNavigation();
      }
    }
  }

  public async clearTripStatus(): Promise<void> {
    this.currentStatus = null;
    await AsyncStorage.multiRemove([
      'tripStatus',
      'tripId',
      'rideStatus',
      'noDrivers',
      'driverCanceled',
      'noDriversTimestamp',
      'pendingDirectNavigation'
    ]);
    this.statusListeners.forEach(listener => listener(null));
    console.log(`[TripStatusManager] Trip status cleared completely`);
  }

  public async clearTripStatusForNewRide(): Promise<void> {
    // Clear current status but don't notify listeners to avoid triggering navigation
    this.currentStatus = null;
    await AsyncStorage.multiRemove([
      'tripStatus',
      'tripId',
      'rideStatus',
      'noDrivers',
      'driverCanceled',
      'noDriversTimestamp',
      'pendingDirectNavigation'
    ]);
    // Don't notify listeners to avoid triggering unwanted navigation
    console.log(`[TripStatusManager] Trip status cleared for new ride (silent)`);
  }

  public getCurrentStatus(): TripStatus | null {
    return this.currentStatus;
  }

  public async loadStoredStatus(): Promise<TripStatus | null> {
    try {
      const stored = await AsyncStorage.getItem('tripStatus');
      if (stored) {
        this.currentStatus = JSON.parse(stored);
        return this.currentStatus;
      }
    } catch (error) {
      console.error('[TripStatusManager] Error loading stored status:', error);
    }
    return null;
  }

  public addStatusListener(listener: (status: TripStatus | null) => void): () => void {
    this.statusListeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.statusListeners.indexOf(listener);
      if (index > -1) {
        this.statusListeners.splice(index, 1);
      }
    };
  }

  public async handleNoDriversAvailable(): Promise<void> {
    console.log('[TripStatusManager] Handling no drivers available');

    const currentScreen = navigationRef.current?.getCurrentRoute()?.name;
    console.log('[TripStatusManager] Current screen:', currentScreen);

    // If we're in Confirm screen, navigate to Direction
    if (currentScreen === 'Confirm') {
      console.log('[TripStatusManager] In Confirm screen, navigating to Direction for no drivers');

      // Clear trip state
      await this.forceResetForNewRide();

      // Set no drivers flag temporarily
      await AsyncStorage.setItem('noDrivers', 'true');
      await AsyncStorage.setItem('noDriversTimestamp', Date.now().toString());

      // Navigate to Direction
      if (await this.navigationGuard.canNavigate('Direction')) {
        await this.navigationGuard.startNavigation('Direction');
        navigationRef.current?.reset({
          index: 0,
          routes: [{ name: 'Direction' }],
        });
        setTimeout(() => this.navigationGuard.completeNavigation(), 500);
      }

      // Clear the no drivers flag after navigation
      setTimeout(async () => {
        await AsyncStorage.removeItem('noDrivers');
        await AsyncStorage.removeItem('noDriversTimestamp');
        console.log('[TripStatusManager] Cleared no drivers flag - ready for new rides');
      }, 2000);
    } else {
      // If we're already in Direction or other screen, just clear state
      console.log('[TripStatusManager] Not in Confirm screen, just clearing state');
      await this.forceResetForNewRide();

      // Update status without navigation
      await this.updateTripStatus('no_drivers_available', undefined, false);
    }
  }

  public async handleDriverCancelled(): Promise<void> {
    console.log('[TripStatusManager] Handling driver cancelled - clearing all state');

    // Immediately clear all trip-related state
    await this.forceResetForNewRide();

    // Update status without auto-navigation
    await this.updateTripStatus('driver_cancelled', undefined, false);

    // Clear state after a short delay
    setTimeout(async () => {
      await this.forceResetForNewRide();
      console.log('[TripStatusManager] Cleared driver cancelled state - ready for new rides');
    }, 2000);
  }

  public async isNoDriversScenario(): Promise<boolean> {
    const noDrivers = await AsyncStorage.getItem('noDrivers');
    return noDrivers === 'true';
  }

  public async clearNoDriversFlag(): Promise<void> {
    await AsyncStorage.removeItem('noDrivers');
    await AsyncStorage.removeItem('noDriversTimestamp');
  }

  public async forceResetForNewRide(): Promise<void> {
    console.log('[TripStatusManager] Force resetting all state for new ride');

    // Clear current status completely
    this.currentStatus = null;

    // Clear all trip-related AsyncStorage keys
    await AsyncStorage.multiRemove([
      'tripStatus',
      'tripId',
      'rideStatus',
      'noDrivers',
      'driverCanceled',
      'noDriversTimestamp',
      'pendingDirectNavigation',
      'currentNavigation',
      'pendingNavigation',
      'driverHalted',
      'driverHaltedTimestamp',
      'driverStillMovingTimestamp',
      'driverArrived',
      'driverNearby',
      'newMessage',
      'rideAborted',
    ]);

    // Clear navigation state
    await this.navigationGuard.clearNavigationState();

    // Notify listeners that state is cleared (silent clear)
    // Don't notify to avoid unwanted navigation

    console.log('[TripStatusManager] Force reset completed - app ready for new ride');
  }

  public async isReadyForNewRide(): Promise<boolean> {
    try {
      const currentStatus = this.getCurrentStatus();
      const tripId = await AsyncStorage.getItem('tripId');
      const noDrivers = await AsyncStorage.getItem('noDrivers');
      const driverCanceled = await AsyncStorage.getItem('driverCanceled');

      // Ready for new ride if:
      // 1. No current status or status is terminal (completed, aborted, driver_cancelled, no_drivers_available)
      // 2. No active trip ID
      // 3. No pending flags

      const terminalStatuses = ['completed', 'aborted', 'driver_cancelled', 'no_drivers_available'];
      const hasTerminalStatus = !currentStatus || terminalStatuses.includes(currentStatus.status);
      const hasNoActiveTripId = !tripId;
      const hasNoPendingFlags = !noDrivers && !driverCanceled;

      const isReady = hasTerminalStatus && hasNoActiveTripId && hasNoPendingFlags;

      console.log(`[TripStatusManager] Ready for new ride: ${isReady}`, {
        currentStatus: currentStatus?.status,
        tripId,
        noDrivers,
        driverCanceled
      });

      return isReady;
    } catch (error) {
      console.error('[TripStatusManager] Error checking if ready for new ride:', error);
      return false;
    }
  }
}

export default TripStatusManager;
