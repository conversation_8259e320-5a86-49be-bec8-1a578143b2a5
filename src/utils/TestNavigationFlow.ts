import AsyncStorage from '@react-native-async-storage/async-storage';
import TripStatusManager from './TripStatusManager';
import NavigationGuard from './NavigationGuard';

/**
 * Test the exact scenario you described:
 * 1. Create ride -> goes to Confirm
 * 2. Driver accepts -> goes to RideDetails  
 * 3. Driver cancels -> goes to Direction
 * 4. No drivers available -> stays in Direction
 * 5. Create new ride -> should go to <PERSON>firm but stays in Direction
 */
export class TestNavigationFlow {
  private tripStatusManager = TripStatusManager.getInstance();
  private navigationGuard = NavigationGuard.getInstance();

  public async testProblemScenario(): Promise<void> {
    console.log('🧪 [Test] Starting problem scenario test...');
    
    try {
      // Step 1: Clear all state first
      await this.clearAllState();
      console.log('🧪 [Test] Step 1: Cleared all state');
      
      // Step 2: Create ride (processing status)
      await this.tripStatusManager.updateTripStatus('processing', 'trip-1', false);
      console.log('🧪 [Test] Step 2: Created ride with processing status');
      await this.logCurrentState();
      
      // Step 3: Driver accepts
      await this.tripStatusManager.updateTripStatus('accepted', 'trip-1', false);
      console.log('🧪 [Test] Step 3: Driver accepted');
      await this.logCurrentState();
      
      // Step 4: Driver cancels
      await this.tripStatusManager.handleDriverCancelled();
      console.log('🧪 [Test] Step 4: Driver cancelled');
      await this.logCurrentState();
      
      // Step 5: No drivers available
      await this.tripStatusManager.handleNoDriversAvailable();
      console.log('🧪 [Test] Step 5: No drivers available');
      await this.logCurrentState();
      
      // Step 6: Check if ready for new ride
      const isReady = await this.tripStatusManager.isReadyForNewRide();
      console.log('🧪 [Test] Step 6: Ready for new ride?', isReady);
      
      // Step 7: Try to create new ride
      await this.tripStatusManager.updateTripStatus('processing', 'trip-2', false);
      console.log('🧪 [Test] Step 7: Created new ride');
      await this.logCurrentState();
      
      console.log('🧪 [Test] Problem scenario test completed');
      
    } catch (error) {
      console.error('🧪 [Test] Error during test:', error);
    }
  }

  private async clearAllState(): Promise<void> {
    await this.tripStatusManager.clearTripStatus();
    await this.navigationGuard.clearNavigationState();
    await AsyncStorage.multiRemove([
      'pendingDirectNavigation',
      'driverHalted',
      'driverHaltedTimestamp',
      'driverStillMovingTimestamp',
    ]);
  }

  private async logCurrentState(): Promise<void> {
    try {
      const currentStatus = this.tripStatusManager.getCurrentStatus();
      const isNavigating = this.navigationGuard.isCurrentlyNavigating();
      const isReady = await this.tripStatusManager.isReadyForNewRide();
      const isNoDrivers = await this.tripStatusManager.isNoDriversScenario();
      
      const asyncStorageKeys = [
        'tripStatus', 'tripId', 'rideStatus', 'noDrivers', 
        'driverCanceled', 'currentNavigation', 'pendingNavigation'
      ];
      const asyncStorageValues = await AsyncStorage.multiGet(asyncStorageKeys);
      const asyncStorageState = Object.fromEntries(asyncStorageValues);
      
      console.log('🧪 [Test] Current State:', {
        tripStatus: currentStatus?.status,
        tripId: currentStatus?.tripId,
        isNavigating,
        isReady,
        isNoDrivers,
        asyncStorage: asyncStorageState,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('🧪 [Test] Error logging state:', error);
    }
  }

  /**
   * Test what happens when we try to create a ride in different states
   */
  public async testRideCreationInDifferentStates(): Promise<void> {
    console.log('🧪 [Test] Testing ride creation in different states...');
    
    const testStates = [
      { status: 'driver_cancelled', description: 'After driver cancelled' },
      { status: 'no_drivers_available', description: 'After no drivers available' },
      { status: 'completed', description: 'After ride completed' },
      { status: 'aborted', description: 'After ride aborted' },
    ];
    
    for (const testState of testStates) {
      console.log(`🧪 [Test] Testing: ${testState.description}`);
      
      // Clear state
      await this.clearAllState();
      
      // Set the test state
      await this.tripStatusManager.updateTripStatus(testState.status, 'test-trip', false);
      
      // Check if ready for new ride
      const isReady = await this.tripStatusManager.isReadyForNewRide();
      console.log(`🧪 [Test] Ready for new ride after ${testState.status}:`, isReady);
      
      // Try to create new ride
      if (isReady) {
        await this.tripStatusManager.updateTripStatus('processing', 'new-trip', false);
        console.log(`🧪 [Test] Successfully created new ride after ${testState.status}`);
      } else {
        console.log(`🧪 [Test] Cannot create new ride after ${testState.status}`);
      }
      
      await this.logCurrentState();
    }
  }

  /**
   * Simulate the exact user flow
   */
  public async simulateUserFlow(): Promise<void> {
    console.log('🧪 [Test] Simulating exact user flow...');
    
    // Start fresh
    await this.clearAllState();
    
    // User creates ride
    console.log('🧪 [Test] User creates ride...');
    await this.tripStatusManager.updateTripStatus('processing', 'user-trip-1', false);
    
    // Simulate some time passing
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Driver accepts
    console.log('🧪 [Test] Driver accepts...');
    await this.tripStatusManager.updateTripStatus('accepted', 'user-trip-1', false);
    
    // Simulate some time passing
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Driver cancels
    console.log('🧪 [Test] Driver cancels...');
    await this.tripStatusManager.updateTripStatus('driver_cancelled', 'user-trip-1', false);
    
    // Simulate some time passing
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // No drivers available
    console.log('🧪 [Test] No drivers available...');
    await this.tripStatusManager.updateTripStatus('no_drivers_available', undefined, false);
    
    // Simulate user waiting and then trying again
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // User tries to create new ride
    console.log('🧪 [Test] User tries to create new ride...');
    const isReady = await this.tripStatusManager.isReadyForNewRide();
    console.log('🧪 [Test] Is ready for new ride?', isReady);
    
    if (isReady) {
      await this.tripStatusManager.updateTripStatus('processing', 'user-trip-2', false);
      console.log('🧪 [Test] New ride created successfully!');
    } else {
      console.log('🧪 [Test] Cannot create new ride - app not ready');
      // Force clear and try again
      await this.clearAllState();
      await this.tripStatusManager.updateTripStatus('processing', 'user-trip-2', false);
      console.log('🧪 [Test] New ride created after force clear');
    }
    
    await this.logCurrentState();
  }
}

export default TestNavigationFlow;
