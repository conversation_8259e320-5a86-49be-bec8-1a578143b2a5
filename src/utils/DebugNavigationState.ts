import AsyncStorage from '@react-native-async-storage/async-storage';
import TripStatusManager from './TripStatusManager';
import NavigationGuard from './NavigationGuard';

/**
 * Debug utility to check current navigation and trip state
 */
export class DebugNavigationState {
  private static instance: DebugNavigationState;
  private tripStatusManager = TripStatusManager.getInstance();
  private navigationGuard = NavigationGuard.getInstance();

  private constructor() {}

  public static getInstance(): DebugNavigationState {
    if (!DebugNavigationState.instance) {
      DebugNavigationState.instance = new DebugNavigationState();
    }
    return DebugNavigationState.instance;
  }

  /**
   * Get complete current state for debugging
   */
  public async getCurrentState(): Promise<any> {
    try {
      const asyncStorageKeys = [
        'tripStatus',
        'tripId', 
        'rideStatus',
        'noDrivers',
        'driverCanceled',
        'noDriversTimestamp',
        'pendingDirectNavigation',
        'currentNavigation',
        'pendingNavigation'
      ];

      const asyncStorageValues = await AsyncStorage.multiGet(asyncStorageKeys);
      const asyncStorageState = Object.fromEntries(asyncStorageValues);

      const currentTripStatus = this.tripStatusManager.getCurrentStatus();
      const isNavigating = this.navigationGuard.isCurrentlyNavigating();
      const isReadyForNewRide = await this.tripStatusManager.isReadyForNewRide();
      const isNoDriversScenario = await this.tripStatusManager.isNoDriversScenario();

      const state = {
        timestamp: new Date().toISOString(),
        tripStatusManager: {
          currentStatus: currentTripStatus,
          isReadyForNewRide,
          isNoDriversScenario,
        },
        navigationGuard: {
          isNavigating,
        },
        asyncStorage: asyncStorageState,
      };

      console.log('🔍 [Debug] Current Navigation State:', JSON.stringify(state, null, 2));
      return state;
    } catch (error) {
      console.error('🔍 [Debug] Error getting current state:', error);
      return { error: error.message };
    }
  }

  /**
   * Force clear all state (use with caution)
   */
  public async forceClearAllState(): Promise<void> {
    try {
      console.log('🧹 [Debug] Force clearing all navigation state...');
      
      await this.tripStatusManager.clearTripStatus();
      await this.navigationGuard.clearNavigationState();
      
      // Clear additional flags that might be lingering
      await AsyncStorage.multiRemove([
        'pendingDirectNavigation',
        'driverHalted',
        'driverHaltedTimestamp',
        'driverStillMovingTimestamp',
        'messagesViewedTimestamp',
        'rideCompletedTimestamp',
        'commissionProcessed',
        'commissionTimestamp',
        'referralRewardProcessed',
        'referralRewardTimestamp',
        'tripleTreatRewardProcessed',
        'tripleTreatTimestamp',
        'welcomeRewardProcessed',
        'welcomeRewardTimestamp',
      ]);

      console.log('🧹 [Debug] All state cleared successfully');
    } catch (error) {
      console.error('🧹 [Debug] Error clearing state:', error);
    }
  }

  /**
   * Check if the app is in a problematic state
   */
  public async diagnoseProblems(): Promise<string[]> {
    const problems: string[] = [];
    
    try {
      const state = await this.getCurrentState();
      
      // Check for conflicting states
      if (state.navigationGuard.isNavigating && !state.asyncStorage.currentNavigation) {
        problems.push('NavigationGuard thinks it\'s navigating but no currentNavigation in AsyncStorage');
      }
      
      if (state.asyncStorage.tripId && !state.tripStatusManager.currentStatus) {
        problems.push('TripId exists but no current trip status');
      }
      
      if (state.asyncStorage.noDrivers === 'true' && state.tripStatusManager.currentStatus?.status !== 'no_drivers_available') {
        problems.push('NoDrivers flag set but status is not no_drivers_available');
      }
      
      if (state.asyncStorage.driverCanceled === 'true' && state.tripStatusManager.currentStatus?.status !== 'driver_cancelled') {
        problems.push('DriverCanceled flag set but status is not driver_cancelled');
      }
      
      if (!state.tripStatusManager.isReadyForNewRide && !state.tripStatusManager.currentStatus) {
        problems.push('Not ready for new ride but no current status');
      }

      if (problems.length > 0) {
        console.warn('⚠️ [Debug] Problems detected:', problems);
      } else {
        console.log('✅ [Debug] No problems detected');
      }
      
    } catch (error) {
      problems.push(`Error during diagnosis: ${error.message}`);
    }
    
    return problems;
  }

  /**
   * Auto-fix common problems
   */
  public async autoFix(): Promise<void> {
    try {
      console.log('🔧 [Debug] Running auto-fix...');
      
      const problems = await this.diagnoseProblems();
      
      if (problems.length > 0) {
        console.log('🔧 [Debug] Problems found, clearing all state...');
        await this.forceClearAllState();
      }
      
      console.log('🔧 [Debug] Auto-fix completed');
    } catch (error) {
      console.error('🔧 [Debug] Error during auto-fix:', error);
    }
  }
}

export default DebugNavigationState;
