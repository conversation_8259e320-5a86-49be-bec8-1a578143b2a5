import notifee from '@notifee/react-native';
import {
  playDriverArrived,
  playDriverCanceled,
  playNoDrivers,
  playRideAccepted,
  playRideAborted,
  playRideCompleted,
  playRideVerified,
  playSafetyCheck,
} from './SoundManager';

export const setupiOSCategories = async () => {
  const categories = [
    {
      id: 'chat',
      actions: [
        {
          id: 'reply',
          title: 'Reply',
          input: {
            buttonText: 'Send',
            placeholderText: 'Reply to message...',
          },
        },
        {
          id: 'view',
          title: 'View',
        },
      ],
    },
    {
      id: 'mapto-no-drivers-channel',
      actions: [
        {
          id: 'view',
          title: 'View Details',
        },
        {
          id: 'retry',
          title: 'Try Again',
        },
      ],
    },
    {
      id: 'mapto-channel',
      actions: [
        {
          id: 'view',
          title: 'View Details',
        },
        {
          id: 'retry',
          title: 'Try Again',
        },
      ],
    },
    {
      id: 'mapto-driver-canceled-channel',
      actions: [
        {
          id: 'view',
          title: 'View Details',
        },
        {
          id: 'retry',
          title: 'Try Again',
        },
      ],
    },
    {
      id: 'mapto-driver-arrived-channel',
      actions: [
        {
          id: 'view',
          title: 'View Details',
        },
        {
          id: 'retry',
          title: 'Try Again',
        },
      ],
    },
    {
      id: 'mapto-driver-accepted-channel',
      actions: [
        {
          id: 'view',
          title: 'View Details',
        },
        {
          id: 'retry',
          title: 'Try Again',
        },
      ],
    },
    {
      id: 'mapto-ride-aborted-channel',
      actions: [
        {
          id: 'view',
          title: 'View Details',
        },
        {
          id: 'retry',
          title: 'Try Again',
        },
      ],
    },
    {
      id: 'mapto-safety-check-channel',
      actions: [
        {
          id: 'view',
          title: 'View Details',
        },
        {
          id: 'retry',
          title: 'Try Again',
        },
      ],
    },
    {
      id: 'mapto-ride-completed-channel',
      actions: [
        {
          id: 'view',
          title: 'View Details',
        },
        {
          id: 'retry',
          title: 'Try Again',
        },
      ],
    },
    {
      id: 'ride_messege',
      actions: [
        {
          id: 'reply',
          title: 'Reply',
          input: {
            buttonText: 'Send',
            placeholderText: 'Reply to message...',
          },
        },
        {
          id: 'view',
          title: 'View',
        },
      ],
    },
  ];

  try {
    for (const category of categories) {
      await notifee.setNotificationCategories([category]);
    }
  } catch (error) {
    console.error('Error setting up iOS categories:', error);
  }
};

export const displayiOSNotification = async (title: string, body?: string, data?: any) => {
  try {
    const ios = await notifee.requestPermission({
      provisional: true,
      criticalAlert: false, // Less intrusive
      announcement: true,
    });
    console.log('🔔 iOS permission status:', ios);

    // Check if we have any level of permission
    if (ios.authorizationStatus === 0) { // Denied
      console.log('⚠️ iOS notification permission denied - skipping notification');
      return;
    }

    await setupiOSCategories();
  } catch (permissionError) {
    console.log('⚠️ iOS notification permission request failed (non-blocking):', permissionError);
    return;
  }

  let categoryId = 'mapto-channel';

  if (data?.type === 'ride_messege') {
    categoryId = 'chat';
  }

  const notificationConfig = {
    title: title || '',
    body: body || '',
    ios: {
      categoryId,
      foregroundPresentationOptions: {
        alert: true,
        badge: true,
        sound: true,
        banner: true,
        list: true,
      },
      critical: true,
      sound: 'default',
      interruptionLevel: 'critical' as const,
    },
    data: data || {},
  };

  switch (title) {

    case 'RideNoDriversAvailable':
      playNoDrivers();
      notificationConfig.title = 'No Drivers Available';
      notificationConfig.body = 'No drivers are available for your ride request.';
      notificationConfig.ios.categoryId = 'mapto-no-drivers-channel';
      notificationConfig.ios.sound = 'nodrivers.mp3';
      break;

    case 'RideCanceledDriver':
      playDriverCanceled();
      notificationConfig.title = 'Driver Cancelled';
      notificationConfig.body = 'Your driver has cancelled the ride.';
      notificationConfig.ios.categoryId = 'mapto-driver-canceled-channel';
      notificationConfig.ios.sound = 'drivercanceled.mp3';
      break;

    case 'RideDriverArrived':
      playDriverArrived();
      notificationConfig.title = 'Driver Arrived';
      notificationConfig.body = 'Your driver has arrived at your location.';
      notificationConfig.ios.categoryId = 'mapto-driver-arrived-channel';
      notificationConfig.ios.sound = 'driverarrived.mp3';
      break;

    case 'RideAccepted':
      playRideAccepted();
      notificationConfig.title = 'Driver Accepted';
      notificationConfig.body = 'Your driver has accepted the ride request.';
      notificationConfig.ios.categoryId = 'mapto-ride-accepted-channel';
      notificationConfig.ios.sound = 'rideaccepted.mp3';
      break;

    case 'RideCompleted':
      playRideCompleted();
      notificationConfig.title = 'Ride Completed';
      notificationConfig.body = 'Your ride has been completed.';
      notificationConfig.ios.categoryId = 'mapto-ride-completed-channel';
      notificationConfig.ios.sound = 'ridecompleted.mp3';
      break;

    case 'RideOtpVerify':
      playRideVerified();
      notificationConfig.title = 'Ride Verified';
      notificationConfig.body = 'Your Ride has been verified.';
      notificationConfig.ios.categoryId = 'mapto-ride-verified-channel';
      notificationConfig.ios.sound = 'rideverified.mp3';
      break;

    case 'RideDriverHalted':
      // playSafetyCheck();
      // notificationConfig.title = 'Safety Check';
      // notificationConfig.body =
      //   'Your driver has halted for a while. Please check if everything is okay.';
      // notificationConfig.ios.categoryId = 'mapto-safety-check-channel';
      // notificationConfig.ios.sound = 'ridesafetycheck.mp3';
      break;

    case 'RideAborted':
      playRideAborted();
      notificationConfig.title = 'Safety Check';
      notificationConfig.body =
        'Your driver has halted for a while. Please check if everything is okay.';
      notificationConfig.ios.categoryId = 'mapto-ride-aborted-channel';
      notificationConfig.ios.sound = 'rideaborted.mp3';
      break;

    case 'ride_messege':
      notificationConfig.title = data?.sender_type === 'Driver' ? 'Message from Driver' : 'New Message';
      notificationConfig.body = data?.content || body || 'New message received';
      notificationConfig.ios.categoryId = 'chat';
      notificationConfig.ios.sound = 'default';
      break;

    default:
      // Handle generic notifications
      if (title && body) {
        notificationConfig.title = title;
        notificationConfig.body = body;
        notificationConfig.ios.categoryId = 'mapto-channel';
        notificationConfig.ios.sound = 'default';
      }
      break;
  }

  try {
    await notifee.displayNotification(notificationConfig);
    console.log('✅ iOS notification displayed successfully');
  } catch (displayError) {
    console.log('⚠️ Failed to display iOS notification (non-blocking):', displayError);
  }
};
