import AsyncStorage from '@react-native-async-storage/async-storage';

class NavigationGuard {
  private static instance: NavigationGuard;
  private isNavigating: boolean = false;
  private lastNavigationTime: number = 0;
  private readonly NAVIGATION_DEBOUNCE_TIME = 1000; // 1 second

  private constructor() {}

  public static getInstance(): NavigationGuard {
    if (!NavigationGuard.instance) {
      NavigationGuard.instance = new NavigationGuard();
    }
    return NavigationGuard.instance;
  }

  public async canNavigate(routeName: string): Promise<boolean> {
    const currentTime = Date.now();
    
    // Check if we're already navigating
    if (this.isNavigating) {
      console.log(`[NavigationGuard] Navigation blocked - already navigating`);
      return false;
    }

    // Check debounce time
    if (currentTime - this.lastNavigationTime < this.NAVIGATION_DEBOUNCE_TIME) {
      console.log(`[NavigationGuard] Navigation blocked - debounce time not met`);
      return false;
    }

    // Check if there's a pending navigation
    const pendingNavigation = await AsyncStorage.getItem('pendingNavigation');
    if (pendingNavigation && pendingNavigation !== routeName) {
      console.log(`[NavigationGuard] Navigation blocked - pending navigation exists: ${pendingNavigation}`);
      return false;
    }

    return true;
  }

  public async startNavigation(routeName: string): Promise<void> {
    this.isNavigating = true;
    this.lastNavigationTime = Date.now();
    await AsyncStorage.setItem('currentNavigation', routeName);
    console.log(`[NavigationGuard] Navigation started to: ${routeName}`);
  }

  public async completeNavigation(): Promise<void> {
    this.isNavigating = false;
    await AsyncStorage.removeItem('currentNavigation');
    console.log(`[NavigationGuard] Navigation completed`);
  }

  public async clearNavigationState(): Promise<void> {
    this.isNavigating = false;
    await AsyncStorage.multiRemove(['currentNavigation', 'pendingNavigation']);
    console.log(`[NavigationGuard] Navigation state cleared`);
  }

  public isCurrentlyNavigating(): boolean {
    return this.isNavigating;
  }
}

export default NavigationGuard;
