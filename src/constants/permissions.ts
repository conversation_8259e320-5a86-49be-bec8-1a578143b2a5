import {Alert, Linking, NativeModules, Platform} from 'react-native';
import {check, PERMISSIONS, request, RESULTS} from 'react-native-permissions';
import messaging from '@react-native-firebase/messaging';
import {navigationRef} from '../router/navigationService';


export const navigateToSettings = () => {
  Alert.alert(
    'Permission Required',
    'This app requires additional permissions. Please go to settings and enable them for full functionality.',
    [
      {text: 'Cancel', style: 'cancel'},
      {text: 'Go to Settings', onPress: () => Linking.openSettings()},
    ],
  );
};

export const requestCallPhonePermission = async () => {
  if (Platform.OS === 'android') {
    const currentStatus = await check(PERMISSIONS.ANDROID.CALL_PHONE);

    if (currentStatus === RESULTS.GRANTED) {
      return true;
    }

    if (currentStatus === RESULTS.DENIED) {
      const result = await request(PERMISSIONS.ANDROID.CALL_PHONE);
      return result === RESULTS.GRANTED;
    }
    return false;
  }

  // iOS does not need call permissions, just return true
  return true;
};

export const requestLocationPermission = async () => {
  try {
    if (Platform.OS === 'ios') {
      const result = await request(PERMISSIONS.IOS.LOCATION_ALWAYS);
      return result === RESULTS.GRANTED;
    } else {
      const backgroundLocationGranted = await request(
        PERMISSIONS.ANDROID.ACCESS_BACKGROUND_LOCATION,
      );
      return backgroundLocationGranted === RESULTS.GRANTED;
    }
  } catch (error) {
    console.error('Error requesting location permission:', error);
    return false;
  }
};

export const requestLocationPermissionWhenInUse = async () => {
  try {
    const permission =
      Platform.OS === 'ios'
        ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE
        : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;

    const result = await request(permission);

    if (result === RESULTS.GRANTED) {
      return true;
    } else if (result === RESULTS.DENIED) {
      return false;
    } else if (result === RESULTS.BLOCKED) {
      return false;
    }
  } catch (error) {
    console.error('Error requesting location permission:', error);
    return false;
  }
};


export const requestNotificationPermission = async () => {
  try {
    if (Platform.OS === 'android') {
      if (Platform.Version >= 33) {
        const result = await request(PERMISSIONS.ANDROID.POST_NOTIFICATIONS);
        if (result === RESULTS.GRANTED) {
          console.log('Android notification permission granted');
          return true;
        } else {
          console.log('Android notification permission denied - app will continue without notifications');
          return false;
        }
      } else {
        console.log(
          'Notification permission not required for Android version below 33',
        );
        return true;
      }
    } else {
      // iOS - Request with provisional option to be less intrusive
      const authStatus = await messaging().requestPermission({
        provisional: true,
        announcement: true,
        criticalAlert: false, // Don't request critical alerts initially
      });

      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        console.log('iOS notification permission granted (including provisional)');
        return true;
      } else {
        console.log('iOS notification permission denied - app will continue without notifications');
        return false;
      }
    }
  } catch (error) {
    console.error('Error requesting notification permission (non-blocking):', error);
    // Always return false on error, but don't block the app
    return false;
  }
};

export const checkForegorundLocationPermission = async () => {
  try {
    let result;

    if (Platform.OS === 'ios') {
      result = await check(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);
    } else if (Platform.OS === 'android') {
      result = await check(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION);
    }

    return result === RESULTS.GRANTED;
  } catch (error) {
    console.error('Error checking location permission:', error);
    return false;
  }
};

export const checkLocationPermission = async () => {
  try {
    if (Platform.OS === 'ios') {
      const result = await check(PERMISSIONS.IOS.LOCATION_ALWAYS);
      return result === RESULTS.GRANTED;
    } else if (Platform.OS === 'android') {
      const backgroundLocation = await check(
        PERMISSIONS.ANDROID.ACCESS_BACKGROUND_LOCATION,
      );
      return backgroundLocation === RESULTS.GRANTED;
    }
    return false;
  } catch (error) {
    console.error('Error checking location permission:', error);
    return false;
  }
};

// Check Notification Permission - Non-blocking
export const checkNotificationPermission = async () => {
  try {
    if (Platform.OS === 'android') {
      if (Platform.Version >= 33) {
        const result = await check(PERMISSIONS.ANDROID.POST_NOTIFICATIONS);
        const granted = result === RESULTS.GRANTED;
        console.log('Android notification permission status:', granted ? 'granted' : 'denied');
        return granted;
      } else {
        console.log(
          'Notification permission not required for Android version below 33',
        );
        return true;
      }
    } else {
      const authStatus = await messaging().hasPermission();
      const granted = (
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL
      );
      console.log('iOS notification permission status:', granted ? 'granted' : 'denied', 'authStatus:', authStatus);
      return granted;
    }
  } catch (error) {
    console.error('Error checking notification permission (non-blocking):', error);
    // Return false but don't block app functionality
    return false;
  }
};
