import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  Dispatch,
  SetStateAction,
  useEffect,
  useRef,
  useCallback,
} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {RideEvents, STATUS_CODE} from '../constants/constants';
import RideService from '../services/RideService';
import {useLoader} from './useLoader';
import TripService from '../services/TripService';
import {useTranslation} from 'react-i18next';
import {Platform, AppState} from 'react-native';
import {navigationRef} from '../router/navigationService';
import {useToast} from '../components/Toast/Toast';
import notifee from '@notifee/react-native';
import {useFocusEffect} from '@react-navigation/native';
import TripStatusManager from '../utils/TripStatusManager';

export type Message = {
  id: number;
  text: string;
  sender: boolean;
  time: string;
  content: string;
};

interface RideDetailsContextProps {
  pickup: string;
  setPickup: (pickup: string) => void;
  destination: string;
  setDestination: (destination: string) => void;
  routeCoordinates: {latitude: number; longitude: number}[];
  setRouteCoordinates: Dispatch<
    SetStateAction<{latitude: number; longitude: number}[]>
  >;
  showRideModal: boolean;
  setShowRideModal: (value: boolean) => void;
  tripDetails: TripData | null;
  setTripDetails: Dispatch<SetStateAction<any>>;
  tripId: any;
  setTripId: Dispatch<SetStateAction<any>>;
  driverDetails: any;
  setDriverDetails: Dispatch<SetStateAction<any>>;
  driverRoute: any;
  setDriverRoute: Dispatch<SetStateAction<any>>;
  timeRemaining: any;
  setTimeRemaining: Dispatch<SetStateAction<any>>;
  messages: Message[];
  setMessages: Dispatch<SetStateAction<Message[]>>;
  handleNotification: (remoteMessage: any) => Promise<void>;
  fetchTripDetails: () => Promise<void>;
  handleTripStatus: () => Promise<void>;
  fetchDriverDetails: () => Promise<void>;
  tripStatus: string | null;
  setTripStatus: Dispatch<SetStateAction<string | null>>;
  clearLocalStorage: () => Promise<void>;
  pausePolling: () => void;
  resumePolling: () => void;
}

const RideDetailsContext = createContext<RideDetailsContextProps | undefined>(
  undefined,
);

interface RideDetailsProviderProps {
  children: ReactNode;
}

export let rideDetails = {
  trip_id: null,
};

export const RideDetailsProvider: React.FC<RideDetailsProviderProps> = ({
  children,
}) => {
  const [pickup, setPickup] = useState<string>('');
  const [destination, setDestination] = useState<string>('');
  const [routeCoordinates, setRouteCoordinates] = useState<
    {latitude: number; longitude: number}[]
  >([]);
  const [showRideModal, setShowRideModal] = useState<boolean>(false);
  const [tripDetails, setTripDetails] = useState<TripData | null>(null);
  const [tripId, setTripId] = useState<string | null>();
  const [driverDetails, setDriverDetails] = useState<DriverDetails | null>(
    null,
  );
  const [driverRoute, setDriverRoute] = useState(null);
  const [timeRemaining, setTimeRemaining] = useState(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const {showLoader, hideLoader} = useLoader();
  const [tripStatus, setTripStatus] = useState<string | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [isPollingPaused, setIsPollingPaused] = useState(false);
  const {t} = useTranslation();
  const {showToast} = useToast();

  const tripStatusManager = TripStatusManager.getInstance();

  // SIMPLE 5-SECOND POLLING - No complex rate limiting

  const navigationLockRef = useRef<{
    lastStatus: string | null;
    lastNavigationTime: number;
    isNavigating: boolean;
    attemptCount: number;
    statusLocks: {
      accepted: boolean;
      verified: boolean;
      completed: boolean;
      driver_cancelled: boolean;
      no_drivers_available: boolean;
      aborted: boolean;
    };
  }>({
    lastStatus: null,
    lastNavigationTime: 0,
    isNavigating: false,
    attemptCount: 0,
    statusLocks: {
      accepted: false,
      verified: false,
      completed: false,
      driver_cancelled: false,
      no_drivers_available: false,
      aborted: false,
    },
  });

  // ULTRA-STRICT NAVIGATION FUNCTION - ONLY PLACE FOR NAVIGATION
  const navigateOnce = async (newStatus: string, reason: string = '') => {
    const lock = navigationLockRef.current;
    const now = Date.now();

    // Increment attempt counter for debugging
    lock.attemptCount++;

    console.log('🔍 Navigation attempt #' + lock.attemptCount + ':', newStatus, 'reason:', reason, 'lock:', {
      isNavigating: lock.isNavigating,
      lastStatus: lock.lastStatus,
      timeSince: now - lock.lastNavigationTime,
      statusLocks: lock.statusLocks
    });

    // ULTRA-STRICT GUARDS - Prevent ANY duplicate navigation
    if (lock.isNavigating) {
      console.log('🚫 Navigation BLOCKED - already navigating');
      return false;
    }

    // SPECIAL GUARD for ALL status types - prevent multiple calls for any status
    const statusKey1 = newStatus as keyof typeof lock.statusLocks;
    if (lock.statusLocks[statusKey1]) {
      console.log('🚫 Navigation BLOCKED - status', newStatus, 'already being processed');
      return false;
    }

    if (lock.lastStatus === newStatus) {
      console.log('🚫 Navigation BLOCKED - same status:', newStatus);
      return false;
    }

    // Increased cooldown to 5 seconds for more aggressive blocking
    if (now - lock.lastNavigationTime < 5000) {
      console.log('🚫 Navigation BLOCKED - too soon (', now - lock.lastNavigationTime, 'ms) - need 5000ms');
      return false;
    }

    // Check current screen
    const currentRoute = navigationRef.current?.getCurrentRoute()?.name;
    const targetScreen = getTargetScreen(newStatus);

    console.log('📍 Current screen:', currentRoute, '→ Target:', targetScreen);

    if (currentRoute === targetScreen) {
      console.log('🚫 Navigation BLOCKED - already on target screen:', targetScreen);
      return false;
    }

    // IMMEDIATE LOCK - Set all flags immediately to prevent race conditions
    lock.isNavigating = true;
    lock.lastStatus = newStatus;
    lock.lastNavigationTime = now;

    // Lock the specific status to prevent multiple calls for same status
    const statusKey = newStatus as keyof typeof lock.statusLocks;
    if (lock.statusLocks[statusKey] !== undefined) {
      lock.statusLocks[statusKey] = true;
      console.log('🔒 STATUS LOCK SET - Blocking all', newStatus, 'navigation for 10 seconds');
    }

    console.log('🔒 NAVIGATION LOCKED - Starting navigation:', newStatus, '→', targetScreen, 'reason:', reason);

    try {
      // Execute navigation based on status
      await executeNavigation(newStatus, currentRoute);
      console.log('✅ Navigation completed successfully for:', newStatus);
      return true;
    } catch (error) {
      console.error('❌ Navigation failed for:', newStatus, error);
      return false;
    } finally {
      // LONGER UNLOCK DELAY to prevent rapid calls
      setTimeout(() => {
        lock.isNavigating = false;
        console.log('🔓 Navigation unlocked after 3 seconds');
      }, 3000);

      // Unlock the specific status after 10 seconds
      if (lock.statusLocks[statusKey] !== undefined) {
        setTimeout(() => {
          lock.statusLocks[statusKey] = false;
          console.log('🔓 STATUS LOCK REMOVED -', newStatus, 'unlocked after 10 seconds');
        }, 10000);
      }
    }
  };

  // Helper function to get target screen for status
  const getTargetScreen = (status: string): string => {
    switch (status) {
      case 'accepted': return 'RideDetails';
      case 'verified': return 'RideRoute';
      case 'completed': return 'CollectCash';
      case 'driver_cancelled': return 'Confirm';
      case 'no_drivers_available': return 'Direction';
      case 'aborted': return 'BottomTab';
      default: return 'Unknown';
    }
  };

  // EXECUTE NAVIGATION - ONLY PLACE WHERE ACTUAL NAVIGATION HAPPENS
  const executeNavigation = async (status: string, currentRoute: string | undefined) => {
    const toastKey = `toast_shown_${status}`;
    const toastAlreadyShown = await AsyncStorage.getItem(toastKey);

    switch (status) {
      case 'no_drivers_available':
        await AsyncStorage.setItem('noDrivers', 'true');
        clearLocalStorage();
        showToast(t('no_drivers_found'), 'failure');
        navigationRef.current?.reset({routes: [{name: 'Direction'}]});
        await AsyncStorage.multiRemove(['tripId', 'noDrivers']);
        break;

      case 'accepted':
        if (!toastAlreadyShown) {
          showToast(t('driver_accept'), 'success');
          await AsyncStorage.setItem(toastKey, 'true');
        }
        await AsyncStorage.setItem('rideStatus', 'ONPICKUP');
        navigationRef.current?.reset({routes: [{name: 'RideDetails'}]});
        break;

      case 'driver_arrived':
        await AsyncStorage.removeItem('driverArrived');
        await AsyncStorage.removeItem('driverNearby');
        showToast(t('driver_arrived'), 'success');
        // No navigation for driver_arrived, just show toast
        break;

      case 'aborted':
        if (!toastAlreadyShown) {
          showToast(t('ride_aborted'), 'failure');
          await AsyncStorage.setItem(toastKey, 'true');
          await clearLocalStorage();
        }
        navigationRef.current?.reset({routes: [{name: 'BottomTab'}]});
        break;

      case 'verified':
        if (!toastAlreadyShown) {
          showToast(t('pin_verified'), 'success');
          await AsyncStorage.setItem(toastKey, 'true');
        }
        await AsyncStorage.setItem('rideStatus', 'ONRIDE');
        navigationRef.current?.reset({routes: [{name: 'RideRoute'}]});
        break;

      case 'driver_cancelled':
        if (!toastAlreadyShown) {
          showToast(t('driver_cancel'), 'failure');
          await AsyncStorage.setItem(toastKey, 'true');
        }
        await AsyncStorage.setItem('driverCanceled', 'true');
        await AsyncStorage.removeItem('rideStatus');
        navigationRef.current?.reset({routes: [{name: 'Confirm'}]});
        break;

      case 'completed':
        if (!toastAlreadyShown) {
          showToast(t('ride_completed'), 'success');
          await AsyncStorage.setItem(toastKey, 'true');
        }
        await AsyncStorage.removeItem('driverHalted');
        await AsyncStorage.setItem('rideStatus', 'COMPLETED');
        navigationRef.current?.reset({routes: [{name: 'CollectCash'}]});
        break;

      default:
        console.log('⚠️ Unknown status for navigation:', status);
        break;
    }
  };

  const handleNotification = async (remoteMessage: any) => {
    const event = remoteMessage?.data?.event;
    const tripIdFromMessage = remoteMessage?.data?.tripId;
    if (tripIdFromMessage)
      await AsyncStorage.setItem('tripId', tripIdFromMessage);

    switch (event) {
      // Add cases for all reward events
      case RideEvents.COMMISSION_PROCESSED:
        showToast(t('commission_processed'), 'success');
        await AsyncStorage.setItem('commissionProcessed', 'true');
        await AsyncStorage.setItem(
          'commissionTimestamp',
          Date.now().toString(),
        );
        break;
      case RideEvents.REFERRAL_REWARD_PROCESSED:
        showToast(t('referral_reward_processed'), 'success');
        await AsyncStorage.setItem('referralRewardProcessed', 'true');
        await AsyncStorage.setItem(
          'referralRewardTimestamp',
          Date.now().toString(),
        );
        break;
      case RideEvents.TRIPLE_TREAT_REWARD_PROCESSED:
        showToast(t('triple_treat_reward_processed'), 'success');
        await AsyncStorage.setItem('tripleTreatRewardProcessed', 'true');
        await AsyncStorage.setItem(
          'tripleTreatTimestamp',
          Date.now().toString(),
        );
        break;
      case RideEvents.WELCOME_REWARD_PROCESSED:
        showToast(t('welcome_reward_processed'), 'success');
        await AsyncStorage.setItem('welcomeRewardProcessed', 'true');
        await AsyncStorage.setItem(
          'welcomeRewardTimestamp',
          Date.now().toString(),
        );
        break;
      case RideEvents.RIDE_DRIVER_ARRIVED:
        await AsyncStorage.removeItem('driverArrived');
        await AsyncStorage.removeItem('driverNearby');
        showToast(t('driver_arrived'), 'success');
        break;
      case RideEvents.RIDE_DRIVER_NEARBY:
        await AsyncStorage.removeItem('driverNearby');
        showToast(t('driver_nearby'), 'success');
        break;
      case RideEvents.RIDE_ABORTED:
        // Use SINGLE navigation function
        await navigateOnce('aborted', 'from notification');
        break;
      case RideEvents.RIDE_STATUS_CHECK_DRIVER_RESPONSE:
        await AsyncStorage.setItem('driverHalted', 'true');
        await AsyncStorage.setItem(
          'driverHaltedTimestamp',
          Date.now().toString(),
        );
        break;
      case RideEvents.RIDE_DRIVER_HAULTED:
        await AsyncStorage.setItem('driverHalted', 'true');
        await AsyncStorage.setItem(
          'driverHaltedTimestamp',
          Date.now().toString(),
        );
        break;
      case RideEvents.RIDE_INITIALISED:
        await AsyncStorage.setItem('tripId', remoteMessage.data.tripId);
        setTripId(remoteMessage.data.tripId);
        break;
      case RideEvents.RIDE_ACCEPTED:
        // Use SINGLE navigation function
        await navigateOnce('accepted', 'from notification');
        break;
      case RideEvents.RIDE_OTP_VERIFY: {
        const isVerified = remoteMessage.data.is_verified === 'true';
        if (!isVerified) {
          console.log('not verified');
        } else {
          // Use SINGLE navigation function
          await navigateOnce('verified', 'from notification');
        }
        break;
      }
      case RideEvents.RIDE_COMPLETED:
        // Use SINGLE navigation function
        await navigateOnce('completed', 'from notification');
        break;
      case RideEvents.RIDE_CANCELED:
        showToast(t('user_cancel'), 'failure');
        await clearLocalStorage();
        // Use SINGLE navigation function
        await navigateOnce('no_drivers_available', 'from user cancel');
        break;
      case RideEvents.RIDE_CANCELED_DRIVER:
        showToast(t('driver_cancel'), 'failure');
        await AsyncStorage.setItem('driverCanceled', 'true');
        await AsyncStorage.removeItem('rideStatus');
        await tripStatusManager.handleDriverCancelled();
        break;
      case RideEvents.RIDE_NO_DRIVERS_AVAILABLE:
        console.log('No drivers available notification received');
        try {
          // Only handle no drivers if we don't have an active trip or if trip is in initial state
          const currentStatus = tripStatusManager.getCurrentStatus();
          const currentScreen = navigationRef.current?.getCurrentRoute()?.name;

          console.log('[useRideDetails] No drivers event - current status:', currentStatus?.status, 'screen:', currentScreen);

          // If we're in Confirm screen with a processing ride, this means no drivers for the current ride
          if (currentScreen === 'Confirm' && currentStatus?.status === 'processing') {
            console.log('[useRideDetails] No drivers for current ride in Confirm screen');
            await tripStatusManager.handleNoDriversAvailable();
            setTripDetails(null);
            showToast(t('no_drivers_found'), 'failure');
          } else if (!currentStatus || currentStatus.status === 'no_drivers_available') {
            // Only handle if no current status or already in no drivers state
            console.log('[useRideDetails] Handling no drivers - no active ride');
            await tripStatusManager.handleNoDriversAvailable();
            setTripDetails(null);
            showToast(t('no_drivers_found'), 'failure');
          } else {
            console.log('[useRideDetails] Ignoring no drivers event - active ride exists with status:', currentStatus.status);
          }
        } catch (error) {
          console.error('Error handling no drivers event:', error);
        }
        break;

      case RideEvents.PICKUP_DRIVER_LOCATION_UPDATE:
      case RideEvents.DESTINATION_LOCATION_UPDATE:
        setDriverRoute(remoteMessage?.data?.route);
        setTimeRemaining(remoteMessage?.data?.remainingTime);
        break;
      default:
        break;
    }
  };

  // REMOVED: All old navigation functions replaced with single navigateOnce function
  // This eliminates multiple navigation calls completely

  const handleTripStatus = async () => {
    // Legacy function kept for compatibility - now uses single navigation
    if (!tripStatus) {
      console.log('⚠️ No trip status to handle');
      return;
    }

    console.log('🎯 Legacy handleTripStatus called, using single navigation:', tripStatus);
    await navigateOnce(tripStatus, 'from legacy handler');
  };

  useEffect(() => {
    const startPolling = async () => {
      try {
        console.log('🔄 Starting trip polling for all platforms...');

        // For iOS: Always poll trip details regardless of notification permissions
        // This is critical for iOS when notifications are denied
        let isIOSWithoutNotifications = false;

        if (Platform.OS === 'ios') {
          try {
            const settings = await notifee.getNotificationSettings();
            const hasFullPermission = settings.authorizationStatus === 2; // Authorized
            isIOSWithoutNotifications = !hasFullPermission;

            console.log('📱 iOS Notification Permission Status:', hasFullPermission ? 'granted' : 'denied');

            if (isIOSWithoutNotifications) {
              console.log('🚨 iOS WITHOUT notification permissions - POLLING IS CRITICAL for app functionality');
              console.log('🔄 Trip polling will handle all status updates and navigation');
            } else {
              console.log('✅ iOS WITH notification permissions - polling as backup');
            }
          } catch (permError) {
            console.log('⚠️ Could not check iOS notification permissions - assuming no permissions, enabling polling');
            isIOSWithoutNotifications = true;
          }
        }

        // Always start polling regardless of notification permissions
        // This ensures the app works even without notification permissions

        // Initial fetch
        await fetchTripDetails();

        // Set up INTELLIGENT interval for polling with rate limiting
        // For iOS without notifications, this is the PRIMARY way to get updates
        intervalRef.current = setInterval(async () => {
          // Check current screen - don't poll if in Confirm screen (it has its own polling)
          const currentScreen = navigationRef.current?.getCurrentRoute()?.name;
          if (currentScreen === 'Confirm' || isPollingPaused) {
            console.log('⏰ Skipping polling - Confirm screen has its own polling or polling is paused');
            return;
          }

          if (isIOSWithoutNotifications) {
            console.log('🔄 iOS Polling trip details (PRIMARY method - no notifications)...');
          } else {
            console.log('⏰ Polling trip details (backup method)...');
          }

          await fetchTripDetails();
        }, 5000); // Simple 5-second polling

        console.log('✅ Trip polling started successfully');

        if (isIOSWithoutNotifications) {
          console.log('🎯 iOS POLLING ACTIVE: Trip status changes will trigger navigation automatically');
        }

      } catch (error) {
        console.error('❌ Error in polling setup:', error);
        // Even if there's an error, try to set up basic polling
        // This is especially important for iOS without notifications
        try {
          intervalRef.current = setInterval(async () => {
            const currentScreen = navigationRef.current?.getCurrentRoute()?.name;
            if (currentScreen !== 'Confirm' && !isPollingPaused) {
              console.log('🔄 Fallback polling for iOS...');
              await fetchTripDetails();
            }
          }, 5000); // Simple 5-second fallback polling
          console.log('✅ Fallback polling started (critical for iOS without notifications)');
        } catch (fallbackError) {
          console.error('❌ Even fallback polling failed:', fallbackError);
        }
      }
    };

    startPolling();

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
        console.log('🔄 Trip polling stopped');
      }
    };
  }, []);

  // DISABLED: useEffect that was causing multiple navigations
  // Navigation is now handled directly in fetchTripDetails to prevent duplicates

  // Keep refs for potential future use
  const lastProcessedStatusRef = useRef<string | null>(null);
  const navigationInProgressRef = useRef<boolean>(false);
  const lastNavigationTimeRef = useRef<number>(0);

  // COMMENTED OUT to prevent multiple navigation triggers
  // useEffect(() => {
  //   // This was causing 6-7 navigation calls
  //   // Now using direct navigation in fetchTripDetails
  // }, [tripStatus]);

  const clearLocalStorage = async () => {
    setTripDetails(null);

    try {
      const keysToRemove = [
        'tripId',
        'rideStatus',
        'driverCanceled',
        'newMessage',
        'rideAborted',
        'driverArrived',
        'driverNearby',
      ];

      await AsyncStorage.multiRemove(keysToRemove);

      const notificationId = await AsyncStorage.getItem('notificationId');
      if (notificationId) {
        await notifee.cancelDisplayedNotification(notificationId);
      }
    } catch (error) {
      console.error('Error clearing local storage:', error);
    }
  };

  // DEBOUNCED pause/resume to prevent rapid cycling
  const pauseTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const resumeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const pausePolling = () => {
    // Clear any pending resume
    if (resumeTimeoutRef.current) {
      clearTimeout(resumeTimeoutRef.current);
      resumeTimeoutRef.current = null;
    }

    // Debounce pause calls
    if (pauseTimeoutRef.current) {
      clearTimeout(pauseTimeoutRef.current);
    }

    pauseTimeoutRef.current = setTimeout(() => {
      if (!isPollingPaused) {
        console.log('[useRideDetails] Pausing polling');
        setIsPollingPaused(true);
      }
      pauseTimeoutRef.current = null;
    }, 100); // 100ms debounce
  };

  const resumePolling = () => {
    // Clear any pending pause
    if (pauseTimeoutRef.current) {
      clearTimeout(pauseTimeoutRef.current);
      pauseTimeoutRef.current = null;
    }

    // Debounce resume calls
    if (resumeTimeoutRef.current) {
      clearTimeout(resumeTimeoutRef.current);
    }

    resumeTimeoutRef.current = setTimeout(() => {
      if (isPollingPaused) {
        console.log('[useRideDetails] Resuming polling');
        setIsPollingPaused(false);
      }
      resumeTimeoutRef.current = null;
    }, 100); // 100ms debounce
  };

  const fetchTripDetails = async () => {
    const storedTripId = await AsyncStorage.getItem('tripId');
    if (storedTripId) {
      try {
        if (Platform.OS === 'ios') {
          console.log('🔍 iOS: Fetching trip details via polling...');
        }

        const response = await TripService.getActiveRide();

        if (response.status === STATUS_CODE.ok) {
          if (!(response.data as any)?.data?.activeRide) {
            console.log('📍 No active ride found - checking last ride');
            await clearLocalStorage();
            const lastRideResponse = await RideService.getLastRide();
            if (lastRideResponse.status === STATUS_CODE.ok) {
              const lastTrip = (lastRideResponse.data as any)?.data?.lastTrip;
              if (lastTrip) {
                console.log('📍 Last trip found:', lastTrip.status);
                // Only update if status actually changed
                if (lastTrip.status !== tripStatus) {
                  setTripDetails(lastTrip);
                  setTripStatus(lastTrip.status);
                  // handleTripStatus will be called by the useEffect that watches tripStatus
                }
              }
            }
            navigationRef.current?.reset({routes: [{name: 'Direction'}]});
            return;
          }

          const activeRide = (response.data as any)?.data?.activeRide;
          const newTripStatus = activeRide?.status;

          // Only update if status actually changed to prevent duplicate navigations
          if (newTripStatus && newTripStatus !== tripStatus) {
            if (Platform.OS === 'ios') {
              console.log('📱 iOS: Active ride found via polling');
              console.log('📍 Current trip details:', activeRide);
              console.log('🎯 Status changed from', tripStatus, 'to', newTripStatus);
            }

            // Update trip details first
            setTripDetails(activeRide);

            // Handle navigation immediately here instead of relying on useEffect
            // This prevents multiple triggers from useEffect
            const previousStatus = tripStatus;
            setTripStatus(newTripStatus);

            // Call SINGLE navigation function - ONLY place navigation happens
            await navigateOnce(newTripStatus, 'from polling');

          } else if (Platform.OS === 'ios' && newTripStatus === tripStatus) {
            console.log('📍 iOS: Status unchanged (' + newTripStatus + '), no navigation needed');
          }
        }
      } catch (err: any) {
        console.error('Error fetching trip details:', err);
        if (Platform.OS === 'ios') {
          console.log('⚠️ iOS: Trip polling error (will retry):', err.message);
        }
      }
    } else {
      if (Platform.OS === 'ios') {
        console.log('📍 iOS: No stored trip ID - skipping poll');
      }
    }
  };

  const fetchDriverDetails = async () => {
    try {
      showLoader();
      const response = await RideService.getDriver();
      if (response.status === STATUS_CODE.ok) {
        console.log('driver details', response?.data?.data);
        setDriverDetails(response?.data?.data);
      } else {
        console.error('Failed to fetch driver details:', response);
      }
    } catch (err: any) {
      console.log('no active ride for driver', err.response?.data);

      const status = err?.response?.status;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
    } finally {
      hideLoader();
    }
  };

  useEffect(() => {
    (async () => {
      const tripId = await AsyncStorage.getItem('tripId');
      tripId && (await AsyncStorage.setItem('tripId', tripId));
      await AsyncStorage.setItem('pickup', pickup);
      await AsyncStorage.setItem('destination', destination);
    })();
  }, [tripId]);

  return (
    <RideDetailsContext.Provider
      value={{
        pickup,
        setPickup,
        destination,
        setDestination,
        routeCoordinates,
        setRouteCoordinates,
        showRideModal,
        setShowRideModal,
        tripDetails,
        setTripDetails,
        tripId,
        setTripId,
        driverDetails,
        setDriverDetails,
        driverRoute,
        setDriverRoute,
        setTimeRemaining,
        timeRemaining,
        messages,
        setMessages,
        handleNotification,
        fetchTripDetails,
        handleTripStatus,
        clearLocalStorage,
        fetchDriverDetails,
        tripStatus,
        setTripStatus,
        pausePolling,
        resumePolling,
      }}>
      {children}
    </RideDetailsContext.Provider>
  );
};

export const useRideDetails = () => {
  const context = useContext(RideDetailsContext);
  if (!context) {
    throw new Error('useRideDetails must be used within a RideDetailsProvider');
  }
  return context;
};
