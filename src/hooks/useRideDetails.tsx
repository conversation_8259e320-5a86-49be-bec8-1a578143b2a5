import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  Dispatch,
  SetStateAction,
  useEffect,
  useRef,
  useCallback,
} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {RideEvents, STATUS_CODE} from '../constants/constants';
import RideService from '../services/RideService';
import {useLoader} from './useLoader';
import TripService from '../services/TripService';
import {useTranslation} from 'react-i18next';
import {Platform, AppState} from 'react-native';
import {navigationRef} from '../router/navigationService';
import {useToast} from '../components/Toast/Toast';
import notifee from '@notifee/react-native';
import {useFocusEffect} from '@react-navigation/native';
import TripStatusManager from '../utils/TripStatusManager';

export type Message = {
  id: number;
  text: string;
  sender: boolean;
  time: string;
  content: string;
};

interface RideDetailsContextProps {
  pickup: string;
  setPickup: (pickup: string) => void;
  destination: string;
  setDestination: (destination: string) => void;
  routeCoordinates: {latitude: number; longitude: number}[];
  setRouteCoordinates: Dispatch<
    SetStateAction<{latitude: number; longitude: number}[]>
  >;
  showRideModal: boolean;
  setShowRideModal: (value: boolean) => void;
  tripDetails: TripData | null;
  setTripDetails: Dispatch<SetStateAction<any>>;
  tripId: any;
  setTripId: Dispatch<SetStateAction<any>>;
  driverDetails: any;
  setDriverDetails: Dispatch<SetStateAction<any>>;
  driverRoute: any;
  setDriverRoute: Dispatch<SetStateAction<any>>;
  timeRemaining: any;
  setTimeRemaining: Dispatch<SetStateAction<any>>;
  messages: Message[];
  setMessages: Dispatch<SetStateAction<Message[]>>;
  handleNotification: (remoteMessage: any) => Promise<void>;
  fetchTripDetails: () => Promise<void>;
  handleTripStatus: () => Promise<void>;
  fetchDriverDetails: () => Promise<void>;
  tripStatus: string | null;
  setTripStatus: Dispatch<SetStateAction<string | null>>;
  clearLocalStorage: () => Promise<void>;
  pausePolling: () => void;
  resumePolling: () => void;
}

const RideDetailsContext = createContext<RideDetailsContextProps | undefined>(
  undefined,
);

interface RideDetailsProviderProps {
  children: ReactNode;
}

export let rideDetails = {
  trip_id: null,
};

export const RideDetailsProvider: React.FC<RideDetailsProviderProps> = ({
  children,
}) => {
  const [pickup, setPickup] = useState<string>('');
  const [destination, setDestination] = useState<string>('');
  const [routeCoordinates, setRouteCoordinates] = useState<
    {latitude: number; longitude: number}[]
  >([]);
  const [showRideModal, setShowRideModal] = useState<boolean>(false);
  const [tripDetails, setTripDetails] = useState<TripData | null>(null);
  const [tripId, setTripId] = useState<string | null>();
  const [driverDetails, setDriverDetails] = useState<DriverDetails | null>(
    null,
  );
  const [driverRoute, setDriverRoute] = useState(null);
  const [timeRemaining, setTimeRemaining] = useState(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const {showLoader, hideLoader} = useLoader();
  const [tripStatus, setTripStatus] = useState<string | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [isPollingPaused, setIsPollingPaused] = useState(false);
  const {t} = useTranslation();
  const {showToast} = useToast();

  const tripStatusManager = TripStatusManager.getInstance();

  const handleNotification = async (remoteMessage: any) => {
    const event = remoteMessage?.data?.event;
    const tripIdFromMessage = remoteMessage?.data?.tripId;
    if (tripIdFromMessage)
      await AsyncStorage.setItem('tripId', tripIdFromMessage);

    switch (event) {
      // Add cases for all reward events
      case RideEvents.COMMISSION_PROCESSED:
        showToast(t('commission_processed'), 'success');
        await AsyncStorage.setItem('commissionProcessed', 'true');
        await AsyncStorage.setItem(
          'commissionTimestamp',
          Date.now().toString(),
        );
        break;
      case RideEvents.REFERRAL_REWARD_PROCESSED:
        showToast(t('referral_reward_processed'), 'success');
        await AsyncStorage.setItem('referralRewardProcessed', 'true');
        await AsyncStorage.setItem(
          'referralRewardTimestamp',
          Date.now().toString(),
        );
        break;
      case RideEvents.TRIPLE_TREAT_REWARD_PROCESSED:
        showToast(t('triple_treat_reward_processed'), 'success');
        await AsyncStorage.setItem('tripleTreatRewardProcessed', 'true');
        await AsyncStorage.setItem(
          'tripleTreatTimestamp',
          Date.now().toString(),
        );
        break;
      case RideEvents.WELCOME_REWARD_PROCESSED:
        showToast(t('welcome_reward_processed'), 'success');
        await AsyncStorage.setItem('welcomeRewardProcessed', 'true');
        await AsyncStorage.setItem(
          'welcomeRewardTimestamp',
          Date.now().toString(),
        );
        break;
      case RideEvents.RIDE_DRIVER_ARRIVED:
        await AsyncStorage.removeItem('driverArrived');
        await AsyncStorage.removeItem('driverNearby');
        showToast(t('driver_arrived'), 'success');
        break;
      case RideEvents.RIDE_DRIVER_NEARBY:
        await AsyncStorage.removeItem('driverNearby');
        showToast(t('driver_nearby'), 'success');
        break;
      case RideEvents.RIDE_ABORTED:
        showToast(t('ride_aborted'), 'failure');
        await clearLocalStorage();
        navigationRef.current?.reset({routes: [{name: 'BottomTab'}]});
        break;
      case RideEvents.RIDE_STATUS_CHECK_DRIVER_RESPONSE:
        await AsyncStorage.setItem('driverHalted', 'true');
        await AsyncStorage.setItem(
          'driverHaltedTimestamp',
          Date.now().toString(),
        );
        break;
      case RideEvents.RIDE_DRIVER_HAULTED:
        await AsyncStorage.setItem('driverHalted', 'true');
        await AsyncStorage.setItem(
          'driverHaltedTimestamp',
          Date.now().toString(),
        );
        break;
      case RideEvents.RIDE_INITIALISED:
        await AsyncStorage.setItem('tripId', remoteMessage.data.tripId);
        setTripId(remoteMessage.data.tripId);
        break;
      case RideEvents.RIDE_ACCEPTED:
        showToast(t('driver_accept'), 'success');
        await AsyncStorage.setItem('rideStatus', 'ONPICKUP');
        navigationRef.current?.reset({routes: [{name: 'RideDetails'}]});
        break;
      case RideEvents.RIDE_OTP_VERIFY: {
        const isVerified = remoteMessage.data.is_verified === 'true';
        if (!isVerified) {
          console.log('not verified');
        } else {
          showToast(t('pin_verified'), 'success');
          await AsyncStorage.setItem('rideStatus', 'ONRIDE');
          navigationRef.current?.reset({routes: [{name: 'RideRoute'}]});
        }
        break;
      }
      case RideEvents.RIDE_COMPLETED:
        showToast(t('ride_completed'), 'success');
        await AsyncStorage.setItem('rideStatus', 'COMPLETED');
        await AsyncStorage.setItem(
          'rideCompletedTimestamp',
          Date.now().toString(),
        );
        await AsyncStorage.removeItem('driverHalted');

        navigationRef.current?.reset({routes: [{name: 'CollectCash'}]});
        break;
      case RideEvents.RIDE_CANCELED:
        showToast(t('user_cancel'), 'failure');
        await clearLocalStorage();
        navigationRef.current?.reset({routes: [{name: 'Direction'}]});
        break;
      case RideEvents.RIDE_CANCELED_DRIVER:
        showToast(t('driver_cancel'), 'failure');
        await AsyncStorage.setItem('driverCanceled', 'true');
        await AsyncStorage.removeItem('rideStatus');
        await tripStatusManager.handleDriverCancelled();
        break;
      case RideEvents.RIDE_NO_DRIVERS_AVAILABLE:
        console.log('No drivers available notification received');
        try {
          // Only handle no drivers if we don't have an active trip or if trip is in initial state
          const currentStatus = tripStatusManager.getCurrentStatus();
          const currentScreen = navigationRef.current?.getCurrentRoute()?.name;

          console.log('[useRideDetails] No drivers event - current status:', currentStatus?.status, 'screen:', currentScreen);

          // If we're in Confirm screen with a processing ride, this means no drivers for the current ride
          if (currentScreen === 'Confirm' && currentStatus?.status === 'processing') {
            console.log('[useRideDetails] No drivers for current ride in Confirm screen');
            await tripStatusManager.handleNoDriversAvailable();
            setTripDetails(null);
            showToast(t('no_drivers_found'), 'failure');
          } else if (!currentStatus || currentStatus.status === 'no_drivers_available') {
            // Only handle if no current status or already in no drivers state
            console.log('[useRideDetails] Handling no drivers - no active ride');
            await tripStatusManager.handleNoDriversAvailable();
            setTripDetails(null);
            showToast(t('no_drivers_found'), 'failure');
          } else {
            console.log('[useRideDetails] Ignoring no drivers event - active ride exists with status:', currentStatus.status);
          }
        } catch (error) {
          console.error('Error handling no drivers event:', error);
        }
        break;

      case RideEvents.PICKUP_DRIVER_LOCATION_UPDATE:
      case RideEvents.DESTINATION_LOCATION_UPDATE:
        setDriverRoute(remoteMessage?.data?.route);
        setTimeRemaining(remoteMessage?.data?.remainingTime);
        break;
      default:
        break;
    }
  };

  const handleTripStatus = async () => {
    const toastKey = `toast_shown_${tripStatus}`;
    const toastAlreadyShown = await AsyncStorage.getItem(toastKey);
    console.log('Trip Status:', tripStatus);

    if (tripStatus == 'no_drivers_available') {
      await AsyncStorage.setItem('noDrivers', 'true');
      clearLocalStorage();
      showToast(t('no_drivers_found'), 'failure');
      navigationRef.current?.reset({routes: [{name: 'Direction'}]});
      await AsyncStorage.multiRemove(['tripId', 'noDrivers']);
    } else if (tripStatus == 'accepted') {
      if (!toastAlreadyShown) {
        try {
          showToast(t('driver_accept'), 'success');
          await AsyncStorage.setItem(toastKey, 'true');
        } catch (error) {
          console.error('Toast display error:', error);
        }
      }
      await AsyncStorage.setItem('rideStatus', 'ONPICKUP');
      navigationRef.current?.reset({routes: [{name: 'RideDetails'}]});
    } else if (tripStatus == 'driver_arrived') {
      await AsyncStorage.removeItem('driverArrived');
      await AsyncStorage.removeItem('driverNearby');
      showToast(t('driver_arrived'), 'success');
    } else if (tripStatus == 'aborted') {
      if (!toastAlreadyShown) {
        try {
          showToast(t('ride_aborted'), 'failure');
          await AsyncStorage.setItem(toastKey, 'true');
          await clearLocalStorage();
          navigationRef.current?.reset({routes: [{name: 'BottomTab'}]});
        } catch (error) {
          console.error('Toast display error:', error);
        }
      }
      await AsyncStorage.setItem('rideStatus', 'ONRIDE');
      navigationRef.current?.reset({routes: [{name: 'RideRoute'}]});
    } else if (tripStatus == 'verified') {
      if (!toastAlreadyShown) {
        try {
          showToast(t('pin_verified'), 'success');
          await AsyncStorage.setItem(toastKey, 'true');
        } catch (error) {
          console.error('Toast display error:', error);
        }
      }
      await AsyncStorage.setItem('rideStatus', 'ONRIDE');
      navigationRef.current?.reset({routes: [{name: 'RideRoute'}]});
    } else if (tripStatus == 'driver_cancelled') {
      if (!toastAlreadyShown) {
        try {
          showToast(t('driver_cancel'), 'failure');
          await AsyncStorage.setItem(toastKey, 'true');
        } catch (error) {
          console.error('Toast display error:', error);
        }
      }
      await AsyncStorage.setItem('driverCanceled', 'true');
      await AsyncStorage.removeItem('rideStatus');
      navigationRef.current?.reset({routes: [{name: 'Confirm'}]});
    } else if (tripStatus == 'completed') {
      if (!toastAlreadyShown) {
        try {
          showToast(t('ride_completed'), 'success');
          await AsyncStorage.setItem(toastKey, 'true');
        } catch (error) {
          console.error('Toast display error:', error);
        }
      }
      await AsyncStorage.removeItem('driverHalted');

      await AsyncStorage.setItem('rideStatus', 'COMPLETED');
      navigationRef.current?.reset({routes: [{name: 'CollectCash'}]});
    }
  };

  useEffect(() => {
    const startPolling = async () => {
      if (Platform.OS !== 'ios') return;

      try {
        const settings = await notifee.getNotificationSettings();
        const hasFullPermission = settings.authorizationStatus === 2;
        console.log('📱 Notification Permission Status:', hasFullPermission);

        if (!hasFullPermission) {
          console.log('🔔 Starting trip polling - No notification permission');

          // Initial fetch
          await fetchTripDetails();

          // Set up interval for polling
          intervalRef.current = setInterval(async () => {
            // Check current screen - don't poll if in Confirm screen (it has its own polling)
            const currentScreen = navigationRef.current?.getCurrentRoute()?.name;
            if (currentScreen === 'Confirm' || isPollingPaused) {
              console.log('⏰ Skipping polling - Confirm screen has its own polling or polling is paused');
              return;
            }

            console.log('⏰ Polling trip details...');
            await fetchTripDetails();
          }, 5000);
        }
      } catch (error) {
        console.error('❌ Error in polling setup:', error);
      }
    };

    startPolling();

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, []);

  const clearLocalStorage = async () => {
    setTripDetails(null);

    try {
      const keysToRemove = [
        'tripId',
        'rideStatus',
        'driverCanceled',
        'newMessage',
        'rideAborted',
        'driverArrived',
        'driverNearby',
      ];

      await AsyncStorage.multiRemove(keysToRemove);

      const notificationId = await AsyncStorage.getItem('notificationId');
      if (notificationId) {
        await notifee.cancelDisplayedNotification(notificationId);
      }
    } catch (error) {
      console.error('Error clearing local storage:', error);
    }
  };

  const pausePolling = () => {
    console.log('[useRideDetails] Pausing polling');
    setIsPollingPaused(true);
  };

  const resumePolling = () => {
    console.log('[useRideDetails] Resuming polling');
    setIsPollingPaused(false);
  };

  const fetchTripDetails = async () => {
    const storedTripId = await AsyncStorage.getItem('tripId');
    if (storedTripId) {
      try {
        const response = await TripService.getActiveRide();

        if (response.status === STATUS_CODE.ok) {
          if (!response.data.data.activeRide) {
            await clearLocalStorage();
            const lastRideResponse = await RideService.getLastRide();
            if (lastRideResponse.status === STATUS_CODE.ok) {
              const lastTrip = lastRideResponse.data.data.lastTrip;
              // Handle both updates together
              await Promise.all([
                setTripDetails(lastTrip),
                setTripStatus(lastTrip.status),
                handleTripStatus(lastTrip.status),
              ]);
            }
            navigationRef.current?.reset({routes: [{name: 'Direction'}]});
            return;
          }

          const activeRide = response.data.data.activeRide;
          const newTripStatus = activeRide.status;

          console.log('📍 Current trip details:', activeRide);
          console.log('📍 New status:', newTripStatus);

          // Handle all updates together and pass status directly
          await Promise.all([
            setTripDetails(activeRide),
            setTripStatus(newTripStatus),
            handleTripStatus(newTripStatus), // Pass status directly
          ]);
        }
      } catch (err: any) {
        console.error('Error fetching trip details:', err);
      }
    }
  };

  const fetchDriverDetails = async () => {
    try {
      showLoader();
      const response = await RideService.getDriver();
      if (response.status === STATUS_CODE.ok) {
        console.log('driver details', response?.data?.data);
        setDriverDetails(response?.data?.data);
      } else {
        console.error('Failed to fetch driver details:', response);
      }
    } catch (err: any) {
      console.log('no active ride for driver', err.response?.data);

      const status = err?.response?.status;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
    } finally {
      hideLoader();
    }
  };

  useEffect(() => {
    (async () => {
      const tripId = await AsyncStorage.getItem('tripId');
      tripId && (await AsyncStorage.setItem('tripId', tripId));
      await AsyncStorage.setItem('pickup', pickup);
      await AsyncStorage.setItem('destination', destination);
    })();
  }, [tripId]);

  return (
    <RideDetailsContext.Provider
      value={{
        pickup,
        setPickup,
        destination,
        setDestination,
        routeCoordinates,
        setRouteCoordinates,
        showRideModal,
        setShowRideModal,
        tripDetails,
        setTripDetails,
        tripId,
        setTripId,
        driverDetails,
        setDriverDetails,
        driverRoute,
        setDriverRoute,
        setTimeRemaining,
        timeRemaining,
        messages,
        setMessages,
        handleNotification,
        fetchTripDetails,
        handleTripStatus,
        clearLocalStorage,
        fetchDriverDetails,
        tripStatus,
        setTripStatus,
        pausePolling,
        resumePolling,
      }}>
      {children}
    </RideDetailsContext.Provider>
  );
};

export const useRideDetails = () => {
  const context = useContext(RideDetailsContext);
  if (!context) {
    throw new Error('useRideDetails must be used within a RideDetailsProvider');
  }
  return context;
};
