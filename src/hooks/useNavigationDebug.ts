import { useEffect, useRef } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import TripStatusManager from '../utils/TripStatusManager';
import NavigationGuard from '../utils/NavigationGuard';

/**
 * Debug hook to track navigation and trip status changes
 */
export const useNavigationDebug = (screenName: string) => {
  const tripStatusManager = TripStatusManager.getInstance();
  const navigationGuard = NavigationGuard.getInstance();
  const mountTimeRef = useRef(Date.now());

  // Log when component mounts/unmounts
  useEffect(() => {
    console.log(`🔍 [${screenName}] Component mounted at ${new Date().toISOString()}`);
    
    return () => {
      const duration = Date.now() - mountTimeRef.current;
      console.log(`🔍 [${screenName}] Component unmounted after ${duration}ms`);
    };
  }, [screenName]);

  // Log when screen gains/loses focus
  useFocusEffect(() => {
    console.log(`🔍 [${screenName}] Screen focused at ${new Date().toISOString()}`);
    
    // Log current state when screen is focused
    const logCurrentState = async () => {
      try {
        const currentStatus = tripStatusManager.getCurrentStatus();
        const isNavigating = navigationGuard.isCurrentlyNavigating();
        const isReady = await tripStatusManager.isReadyForNewRide();
        
        console.log(`🔍 [${screenName}] Current state:`, {
          tripStatus: currentStatus?.status,
          tripId: currentStatus?.tripId,
          isNavigating,
          isReady,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error(`🔍 [${screenName}] Error logging state:`, error);
      }
    };
    
    logCurrentState();
    
    return () => {
      console.log(`🔍 [${screenName}] Screen lost focus at ${new Date().toISOString()}`);
    };
  });

  // Listen to trip status changes
  useEffect(() => {
    const unsubscribe = tripStatusManager.addStatusListener((status) => {
      console.log(`🔍 [${screenName}] Trip status changed:`, {
        newStatus: status?.status,
        tripId: status?.tripId,
        timestamp: new Date().toISOString()
      });
    });

    return unsubscribe;
  }, [screenName, tripStatusManager]);

  return {
    logAction: (action: string, data?: any) => {
      console.log(`🔍 [${screenName}] Action: ${action}`, data ? data : '');
    },
    logError: (error: string, details?: any) => {
      console.error(`🔍 [${screenName}] Error: ${error}`, details ? details : '');
    }
  };
};

export default useNavigationDebug;
