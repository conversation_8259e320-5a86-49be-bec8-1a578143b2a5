import messaging from '@react-native-firebase/messaging';
import {STATUS_CODE} from './src/constants/constants';
import AuthService from './src/services/AuthService';

export const getFcmToken = async () => {
  try {
    console.log('🔔 Attempting to get FCM token...');
    const fcmToken = await messaging().getToken();
    if (fcmToken) {
      console.log('✅ FCM token obtained successfully');
      await sendTokenToBackend(fcmToken);
    } else {
      console.log('⚠️ Failed to get FCM token - app will continue without push notifications');
    }
  } catch (error) {
    console.log('⚠️ Error getting FCM token (non-blocking):', error);
    console.log('📱 App will continue to work without push notifications');
  }
};

const sendTokenToBackend = async (token: string) => {
  try {
    console.log('📤 Sending FCM token to backend...');
    const response = await AuthService.updateFcmToken(token);
    if (response.status === STATUS_CODE.created) {
      console.log('✅ FCM token sent to backend successfully');
    }
  } catch (err: any) {
    const status = err?.response?.status;
    const message = err?.response?.data?.message;
    console.log('⚠️ Failed to send FCM token to backend (non-blocking):', message || err.message);
    if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
      console.log('📱 App will continue to work without backend FCM token registration');
      return;
    }
  }
};

// Listen for token refresh
export const setupTokenRefreshListener = () => {
  try {
    messaging().onTokenRefresh(async newToken => {
      console.log('🔄 FCM Token refreshed:', newToken ? 'success' : 'failed');
      if (newToken) {
        await sendTokenToBackend(newToken);
      }
    });
    console.log('✅ FCM token refresh listener set up');
  } catch (error) {
    console.log('⚠️ Failed to set up FCM token refresh listener (non-blocking):', error);
  }
};

export const initializeFcm = async () => {
  try {
    console.log('🚀 Initializing FCM (non-blocking)...');
    await getFcmToken();
    setupTokenRefreshListener();
    console.log('✅ FCM initialization completed');
  } catch (error) {
    console.log('⚠️ FCM initialization failed (non-blocking):', error);
    console.log('📱 App will continue to work without FCM');
  }
};
