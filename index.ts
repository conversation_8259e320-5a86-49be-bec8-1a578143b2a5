/**
 * @format
 */ import {AppRegistry, AppState, Platform} from 'react-native';
import App from './App';
import {name as appName} from './app.json';
import notifee, {
  AndroidImportance,
  AndroidVisibility,
  EventType,
} from '@notifee/react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {rideDetails} from './src/hooks/useRideDetails';
import messaging, {
  FirebaseMessagingTypes,
} from '@react-native-firebase/messaging';
import {
  playDriverCanceled,
  playDriverArrived,
  playNoDrivers,
  stopAllSounds,
  playRideAccepted,
  playRideCompleted,
  playRideVerified,
  playRideAborted,
  // playSafetyCheck, // Safety check function commented out
} from './src/utils/SoundManager';

let messagesViewedTimestamp = 0;

async function clearAllDriverMessageNotifications() {
  try {
    await notifee.cancelNotification('driver-messages');

    const keys = await AsyncStorage.getAllKeys();
    const messageKeys = keys.filter(key => key.startsWith('messages-'));
    if (messageKeys.length > 0) {
      await AsyncStorage.multiRemove(messageKeys);
    }

    messagesViewedTimestamp = Date.now();
    await AsyncStorage.setItem(
      'messagesViewedTimestamp',
      messagesViewedTimestamp.toString(),
    );

    console.log(
      'All driver message notifications cleared at:',
      messagesViewedTimestamp,
    );
  } catch (error) {
    console.error('Error clearing notifications:', error);
  }
}

// Create notification channel
async function createNotificationChannel() {
  await notifee.createChannel({
    id: 'mapto-channel',
    name: 'User Ride Channel',
    lights: true,
    vibration: true,
    importance: AndroidImportance.HIGH,
    sound: 'default',
    visibility: AndroidVisibility.PUBLIC,
  });

  // // Create a specific channel for chat messages
  // await notifee.createChannel({
  //   id: 'chat-messages',
  //   name: 'Chat Messages',
  //   lights: true,
  //   vibration: true,
  //   importance: AndroidImportance.HIGH,
  //   sound: 'default',
  //   visibility: AndroidVisibility.PUBLIC,
  // });

  await notifee.createChannel({
    id: 'mapto-driver-arrived-channel',
    name: 'Driver Arrival Notifications',
    lights: true,
    vibration: true,
    importance: AndroidImportance.HIGH,
    visibility: AndroidVisibility.PUBLIC,
    sound: '',
    bypassDnd: true,
  });

  await notifee.createChannel({
    id: 'mapto-driver-canceled-channel',
    name: 'Driver Cancellation Notifications',
    lights: true,
    vibration: true,
    importance: AndroidImportance.HIGH,
    visibility: AndroidVisibility.PUBLIC,
    bypassDnd: true,
    sound: 'drivercanceled',
  });

  await notifee.createChannel({
    id: 'mapto-no-drivers-channel',
    name: 'No Drivers Available Notifications',
    lights: true,
    vibration: true,
    importance: AndroidImportance.HIGH,
    visibility: AndroidVisibility.PUBLIC,
    bypassDnd: true,
  });
  await notifee.createChannel({
    id: 'mapto-ride-aborted-channel',
    name: 'Ride Aborted Notifications',
    sound: 'rideaborted',
    importance: AndroidImportance.HIGH,
  });

  await notifee.createChannel({
    id: 'mapto-ride-accepted-channel',
    name: 'Ride Accepted Notifications',
    sound: 'rideaccepted',
    importance: AndroidImportance.HIGH,
  });

  await notifee.createChannel({
    id: 'mapto-ride-verified-channel',
    name: 'Ride Verified Notifications',
    sound: 'rideverified',
    importance: AndroidImportance.HIGH,
  });

  await notifee.createChannel({
    id: 'mapto-safety-check-channel',
    name: 'Safety Check Notifications',
    sound: 'safetycheck',
    importance: AndroidImportance.HIGH,
  });

  await notifee.createChannel({
    id: 'mapto-ride-completed-channel',
    name: 'Ride Completed Notifications',
    sound: 'ridecompleted',
    importance: AndroidImportance.HIGH,
  });
}

createNotificationChannel().catch(err => {
  console.error('Error creating notification channel:', err);
});

AppState.addEventListener('change', async nextAppState => {
  if (nextAppState === 'active') {
    isAppInForeground = true;
  } else {
    isAppInForeground = false;
  }
});

notifee.onForegroundEvent(({type, detail}) => {
  if (type === EventType.PRESS) {
    if (detail.notification) {
      notifee.cancelNotification(detail.notification.id);
    }

    if (detail.notification?.data?.type === 'driver-message') {
      AsyncStorage.setItem('messagesViewedTimestamp', Date.now().toString());
    }
  }
});

// Setup background event handler
notifee.onBackgroundEvent(async ({type, detail}) => {
  console.log('Background event:', type, detail);

  if (type === EventType.PRESS) {
    // Clear notification when pressed
    if (detail.notification) {
      await notifee.cancelNotification(detail.notification.id);
    }
  }
});

// async function displayDriverMessage(message, tripId) {
//   try {
//     const storedTimestamp = await AsyncStorage.getItem(
//       'messagesViewedTimestamp',
//     );
//     const lastViewedTimestamp = storedTimestamp ? parseInt(storedTimestamp) : 0;
//     const currentTimestamp = Date.now();

//     if (currentTimestamp > lastViewedTimestamp) {
//       const DRIVER_NOTIFICATION_ID = 'driver-messages';

//       await notifee.displayNotification({
//         id: DRIVER_NOTIFICATION_ID,
//         title: 'Driver',
//         body: message,
//         data: {
//           tripId,
//           timestamp: currentTimestamp,
//           type: 'driver-message',
//         },
//         android: {
//           channelId: 'chat-messages',
//           importance: AndroidImportance.HIGH,
//           smallIcon: 'ic_mapto',
//           pressAction: {
//             id: 'default',
//             launchActivity: 'default',
//           },
//           autoCancel: true,
//           showTimestamp: true,
//           timestamp: currentTimestamp,
//         },
//         ios: {
//           critical: false,
//           sound: 'default',
//           categoryId: 'chat',
//           threadId: 'chat-messages',
//         },
//       });

//       await AsyncStorage.setItem(
//         `messages-${currentTimestamp}`,
//         JSON.stringify({
//           content: message,
//           timestamp: currentTimestamp,
//           id: currentTimestamp,
//           tripId: tripId,
//         }),
//       );
//     }
//   } catch (error) {
//     console.error('Failed to display driver message notification:', error);
//   }
// }

messaging().onMessage(async remoteMessage => {
  console.log('Foreground message received:', remoteMessage);

  const tripIdFromMessage = remoteMessage?.data?.tripId;
  const event = remoteMessage?.data?.event;

  // if (remoteMessage.data && remoteMessage.data.content) {
  //   const messageContent =
  //     typeof remoteMessage.data.content === 'string'
  //       ? remoteMessage.data.content
  //       : JSON.stringify(remoteMessage.data.content);

  //   const newMessage = {
  //     id: Number(new Date()),
  //     text: messageContent,
  //     sender: false,
  //     time: new Date().toLocaleTimeString([], {
  //       hour: '2-digit',
  //       minute: '2-digit',
  //     }),
  //   };

  //   await AsyncStorage.setItem('newMessage', JSON.stringify(newMessage));

  //   await displayDriverMessage(messageContent, tripIdFromMessage || 'unknown');

  //   return;
  // }

  const NOTIFICATION_IDS = {
    RideNoDriversAvailable: 'no-drivers-notification',
    RideAccepted: 'ride-accepted-notification',
    RideCompleted: 'ride-completed-notification',
    RideOtpVerify: 'otp-verified-notification',
    RideCanceledDriver: 'ride-canceled-notification',
    ReferralRewardProcessed: 'referral-reward-notification',
  };

  const notificationId = NOTIFICATION_IDS[event] || `${event}-${Date.now()}`;

  if (event) {
    processRideEvent(event, remoteMessage, notificationId, tripIdFromMessage);
  }
});

// Helper function to process ride events
async function processRideEvent(
  event: string,
  remoteMessage: FirebaseMessagingTypes.RemoteMessage,
  notificationId: string,
  tripIdFromMessage?: string,
) {
  console.log('Processing event:', event);

  // Check if this is a reward notification
  const isRewardNotification = [
    'ReferralRewardProcessed',
    'TripleTreatRewardProcessed',
    'WelcomeRewardProcessed',
  ].includes(event);

  // Generate unique IDs only for reward notifications
  const finalNotificationId = isRewardNotification
    ? `${event}-${Date.now()}-${Math.floor(Math.random() * 10000)}`
    : notificationId;

  if (event === 'ReferralRewardProcessed') {
    await AsyncStorage.setItem('referralRewardProcessed', 'true');
    await AsyncStorage.setItem(
      'referralRewardTimestamp',
      Date.now().toString(),
    );

    await notifee.displayNotification({
      id: finalNotificationId,
      title: 'Referral Bonus Unlocked!',
      body: 'You have just earned a reward for referring a friend — enjoy the perks!',
      android: {
        channelId: 'mapto-channel',
        importance: AndroidImportance.HIGH,
        smallIcon: 'ic_mapto',
        pressAction: {
          id: 'default',
          launchActivity: 'default',
        },
        // Don't auto-cancel reward notifications
        autoCancel: false,
      },
    });
  } else if (event === 'TripleTreatRewardProcessed') {
    await AsyncStorage.setItem('tripleTreatRewardProcessed', 'true');
    await AsyncStorage.setItem('tripleTreatTimestamp', Date.now().toString());

    await notifee.displayNotification({
      id: finalNotificationId,
      title: 'Triple Treat Ride Reward!',
      body: 'A reward for your ride has been credited. Complete 3 rides to unlock the full treat.',
      android: {
        channelId: 'mapto-channel',
        importance: AndroidImportance.HIGH,
        smallIcon: 'ic_mapto',
        pressAction: {
          id: 'default',
          launchActivity: 'default',
        },
        // Don't auto-cancel reward notifications
        autoCancel: false,
      },
    });
  } else if (event === 'WelcomeRewardProcessed') {
    await AsyncStorage.setItem('welcomeRewardProcessed', 'true');
    await AsyncStorage.setItem('welcomeRewardTimestamp', Date.now().toString());

    await notifee.displayNotification({
      id: finalNotificationId,
      title: 'Welcome Aboard!',
      body: 'Your welcome reward is here — thanks for joining us!',
      android: {
        channelId: 'mapto-channel',
        importance: AndroidImportance.HIGH,
        smallIcon: 'ic_mapto',
        pressAction: {
          id: 'default',
          launchActivity: 'default',
        },
        // Don't auto-cancel reward notifications
        autoCancel: false,
      },
    });
  } else if (event === 'RideNoDriversAvailable') {
    await AsyncStorage.setItem('noDrivers', 'true');
    await AsyncStorage.removeItem('tripId');
    playNoDrivers();
    await notifee.displayNotification({
      id: notificationId,
      title: 'No Drivers Available',
      body: 'Sorry, no drivers are available at this time',
      android: {
        channelId: 'mapto-no-drivers-channel',
        importance: AndroidImportance.HIGH,
        smallIcon: 'ic_mapto',
        vibrationPattern: [300, 500],
        pressAction: {
          id: 'default',
          launchActivity: 'default',
        },
      },
    });
  } else if (event === 'RideAccepted') {
    playRideAccepted();
    await AsyncStorage.setItem('rideStatus', 'ONPICKUP');
    await AsyncStorage.setItem('tripId', tripIdFromMessage);
    await AsyncStorage.setItem('notificationId', notificationId);
    rideDetails.trip_id = tripIdFromMessage;

    await notifee.displayNotification({
      id: notificationId,
      title: 'Ride Accepted',
      body: 'Your ride has been accepted by a driver',
      android: {
        channelId: 'mapto-channel',
        importance: AndroidImportance.HIGH,
        smallIcon: 'ic_mapto',
        pressAction: {
          id: 'default',
          launchActivity: 'default',
        },
      },
    });
  } else if (event === 'RideCompleted') {
    playRideCompleted();
    await AsyncStorage.setItem('rideStatus', 'COMPLETED');
    await AsyncStorage.setItem('rideCompletedTimestamp', Date.now().toString());
    await AsyncStorage.removeItem('driverHalted');

    await notifee.displayNotification({
      id: notificationId,
      title: 'Ride Completed',
      body: 'Your ride has been completed',
      android: {
        channelId: 'mapto-channel',
        importance: AndroidImportance.HIGH,
        smallIcon: 'ic_mapto',
        pressAction: {
          id: 'default',
          launchActivity: 'default',
        },
      },
    });
  } else if (event === 'RideOtpVerify') {
    if (remoteMessage.data.is_verified === 'true') {
      await AsyncStorage.removeItem('newMessage');
      await AsyncStorage.setItem('rideStatus', 'ONRIDE');
      playRideVerified();

      await notifee.displayNotification({
        id: notificationId,
        title: 'OTP Verified',
        body: 'Your OTP has been verified. Enjoy your ride!',
        android: {
          channelId: 'mapto-channel',
          importance: AndroidImportance.HIGH,
          smallIcon: 'ic_mapto',
          pressAction: {
            id: 'default',
            launchActivity: 'default',
          },
        },
      });
    }
  } else if (event === 'RideCanceledDriver') {
    await AsyncStorage.removeItem('newMessage');
    await AsyncStorage.removeItem('rideStatus');
    await AsyncStorage.setItem('driverCanceled', 'true');
    playDriverCanceled();
    await notifee.displayNotification({
      id: notificationId,
      title: 'Ride Canceled',
      body: 'Your ride has been canceled by the driver',
      android: {
        channelId: 'mapto-driver-canceled-channel',
        importance: AndroidImportance.HIGH,
        smallIcon: 'ic_mapto',
        vibrationPattern: [300, 500],
        pressAction: {
          id: 'default',
          launchActivity: 'default',
        },
      },
    });
  } else if (
    event === 'RideDriverLocationUpdate' ||
    event === 'RideDriverLocationUpdateDestination'
  ) {
    await AsyncStorage.setItem('locationUpdate', remoteMessage.data.route);
  } else if (event === 'RideDriverNearby') {
    // Store the event for in-app toast
    await AsyncStorage.setItem('driverNearby', 'true');
    await AsyncStorage.setItem('driverNearbyTimestamp', Date.now().toString());

    // await notifee.displayNotification({
    //   id: notificationId,
    //   title: 'Driver Nearby',
    //   body: 'Your driver is nearby. Please be ready.',
    //   android: {
    //     channelId: 'mapto-channel',
    //     importance: AndroidImportance.HIGH,
    //     smallIcon: 'ic_mapto',
    //     pressAction: {
    //       id: 'default',
    //       launchActivity: 'default',
    //     },
    //   },
    // });
  } else if (event === 'RideDriverArrived') {
    await AsyncStorage.removeItem('driverNearby');
    await AsyncStorage.setItem('driverArrived', 'true');
    await AsyncStorage.setItem('driverArrivedTimestamp', Date.now().toString());
    playDriverArrived();
    await notifee.displayNotification({
      id: notificationId,
      title: 'Driver Arrived',
      body: 'Your driver has arrived!',
      android: {
        channelId: 'mapto-driver-arrived-channel',
        importance: AndroidImportance.HIGH,
        smallIcon: 'ic_mapto',
        vibrationPattern: [300, 500],
        pressAction: {
          id: 'default',
          launchActivity: 'default',
        },
      },
    });
  } else if (event === 'RideAborted') {
    // Store the event for in-app toast and handle navigation
    await AsyncStorage.setItem('rideAborted', 'true');
    await AsyncStorage.setItem('rideAbortedTimestamp', Date.now().toString());
    playRideAborted();
    // Display notification
    await notifee.displayNotification({
      id: notificationId,
      title: 'Ride Aborted',
      body: 'Your ride has been aborted since the driver is not moving.Please book another ride.',
      android: {
        channelId: 'mapto-channel',
        importance: AndroidImportance.HIGH,
        smallIcon: 'ic_mapto',
        pressAction: {
          id: 'default',
          launchActivity: 'default',
        },
      },
    });
  } else if (event === 'RideDriverHalted') {
    // await AsyncStorage.setItem('driverHalted', 'true');
    // await AsyncStorage.setItem('driverHaltedTimestamp', Date.now().toString());
    // playSafetyCheck(); // Safety check function commented out
    // await notifee.displayNotification({
    //   id: notificationId,
    //   title: 'Safety Check',
    //   body: 'Your driver has halted for a while. Please check if everything is okay.',
    //   android: {
    //     channelId: 'mapto-channel',
    //     importance: AndroidImportance.HIGH,
    //     smallIcon: 'ic_mapto',
    //     pressAction: {
    //       id: 'default',
    //       launchActivity: 'default',
    //     },
    //   },
    // });
  } else if (event === 'RideStatusCheckDriverResponse') {
    await AsyncStorage.setItem('driverStillMoving', 'true');
    await AsyncStorage.setItem(
      'driverStillMovingTimestamp',
      Date.now().toString(),
    );

    await notifee.displayNotification({
      id: notificationId,
      title: 'Driver Status',
      body: 'Driver is still moving. Please wait for some time.',
      android: {
        channelId: 'mapto-channel',
        importance: AndroidImportance.HIGH,
        smallIcon: 'ic_mapto',
        pressAction: {
          id: 'default',
          launchActivity: 'default',
        },
        ongoing: true,
      },
      ios: {
        critical: true,
        sound: 'default',
        categoryId: 'rideStatus',
        interruptionLevel: 'timeSensitive',
      },
    });
  }

  // Only set timeout for non-reward notifications
  if (!isRewardNotification) {
    setTimeout(() => {
      notifee.cancelNotification(finalNotificationId);
    }, 18000);
  }
}

// Make the background handler more robust
messaging().setBackgroundMessageHandler(async remoteMessage => {
  console.log('📱 Background message received:', remoteMessage);
  const event = remoteMessage?.data?.event;

  if (event === 'RideNoDriversAvailable') {
    try {
      console.log('📱 Background: Handling no drivers available');

      // Use TripStatusManager for consistent handling
      const TripStatusManager = require('./src/utils/TripStatusManager').default;
      const tripStatusManager = TripStatusManager.getInstance();

      // Force reset everything for no drivers scenario
      await tripStatusManager.forceResetForNewRide();
      await tripStatusManager.handleNoDriversAvailable();

      console.log('📱 Background: No drivers handling completed');
    } catch (error) {
      console.error('📱 Background: Error in no drivers handler:', error);
    }
  }

  return processRemoteMessage(remoteMessage);
});

// Extract common message processing logic
async function processRemoteMessage(remoteMessage) {
  const tripIdFromMessage = remoteMessage?.data?.tripId;
  const event = remoteMessage?.data?.event;

  try {
    // Handle message content
    if (remoteMessage.data && remoteMessage.data.content) {
      // ...existing message processing code...
    }

    const NOTIFICATION_IDS = {
      RideNoDriversAvailable: 'no-drivers-notification',
      RideAccepted: 'ride-accepted-notification',
      RideCompleted: 'ride-completed-notification',
      RideOtpVerify: 'otp-verified-notification',
      RideCanceledDriver: 'ride-canceled-notification',
      CommissionProcessed: 'commission-reward-notification',
      ReferralRewardProcessed: 'referral-reward-notification',
      TripleTreatRewardProcessed: 'triple-treat-reward-notification',
      WelcomeRewardProcessed: 'welcome-reward-notification',
    };

    const notificationId = NOTIFICATION_IDS[event] || `${event}-${Date.now()}`;

    await processRideEvent(
      event,
      remoteMessage,
      notificationId,
      tripIdFromMessage,
    );
  } catch (error) {
    console.error('Error in message processing:', error);
  }
}

messaging().setBackgroundMessageHandler(async remoteMessage => {
  console.log('Message handled in the background!', remoteMessage);
});

AppRegistry.registerHeadlessTask(
  'ReactNativeFirebaseMessagingHeadlessTask',
  () => async remoteMessage => {
    console.log('Message handled in the background!', remoteMessage);
    const tripIdFromMessage = remoteMessage?.data?.tripId;
    const event = remoteMessage?.data?.event;

    try {
      if (remoteMessage.data && remoteMessage.data.content) {
        const messageContent =
          typeof remoteMessage.data.content === 'string'
            ? remoteMessage.data.content
            : JSON.stringify(remoteMessage.data.content);

        const newMessage = {
          id: Number(new Date()),
          text: messageContent,
          sender: false,
          time: new Date().toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
          }),
        };

        await AsyncStorage.setItem('newMessage', JSON.stringify(newMessage));

        await displayDriverMessage(
          messageContent,
          tripIdFromMessage || 'unknown',
        );

        return;
      }

      const NOTIFICATION_IDS = {
        RideNoDriversAvailable: 'no-drivers-notification',
        RideAccepted: 'ride-accepted-notification',
        RideCompleted: 'ride-completed-notification',
        RideOtpVerify: 'otp-verified-notification',
        RideCanceledDriver: 'ride-canceled-notification',
      };

      const notificationId =
        NOTIFICATION_IDS[event] || `${event}-${Date.now()}`;

      await processRideEvent(
        event,
        remoteMessage,
        notificationId,
        tripIdFromMessage,
      );
    } catch (error) {
      console.error('Error in background task:', error);
    }
  },
);

AppRegistry.registerComponent(appName, () => App);

export {clearAllDriverMessageNotifications, messagesViewedTimestamp};
